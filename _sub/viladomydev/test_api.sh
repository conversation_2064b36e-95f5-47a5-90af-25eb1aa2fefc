#!/bin/bash

# Test script pro Leads API
echo "=== Testování Leads API ==="
echo ""

# URL endpointu (upravte podle potřeby)
API_URL="https://dev.viladomyumlyna.cz/leads-api/"
API_TOKEN="viladomy_api_2024_secure_token_xyz789"

echo "API URL: $API_URL"
echo "API Token: $API_TOKEN"
echo ""

# Test 1: Úspěšný požadavek
echo "=== Test 1: Úspěšný požadavek ==="
curl -X POST "$API_URL" \
  -H "Content-Type: application/json" \
  -H "X-API-Token: $API_TOKEN" \
  -d '{
    "email": "<EMAIL>",
    "name": "Test",
    "surname": "User",
    "phone": "+420 777 123 456",
    "lang": "cs",
    "flat": "A1-01",
    "question": "Test API call"
  }' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s
echo ""
echo ""

# Test 2: Chybějíc<PERSON> token
echo "=== Test 2: Chybějící API token ==="
curl -X POST "$API_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "name": "Test",
    "surname": "User",
    "phone": "+420 777 123 456",
    "lang": "cs",
    "flat": "A1-01"
  }' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s
echo ""
echo ""

# Test 3: Neplatný token
echo "=== Test 3: Neplatný API token ==="
curl -X POST "$API_URL" \
  -H "Content-Type: application/json" \
  -H "X-API-Token: invalid_token" \
  -d '{
    "email": "<EMAIL>",
    "name": "Test",
    "surname": "User",
    "phone": "+420 777 123 456",
    "lang": "cs",
    "flat": "A1-01"
  }' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s
echo ""
echo ""

# Test 4: Chybějící povinná pole
echo "=== Test 4: Chybějící povinná pole ==="
curl -X POST "$API_URL" \
  -H "Content-Type: application/json" \
  -H "X-API-Token: $API_TOKEN" \
  -d '{
    "email": "<EMAIL>",
    "name": "Test"
  }' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s
echo ""
echo ""

# Test 5: GET požadavek (měl by být zamítnut)
echo "=== Test 5: GET požadavek (měl by být zamítnut) ==="
curl -X GET "$API_URL" \
  -H "X-API-Token: $API_TOKEN" \
  -w "\nHTTP Status: %{http_code}\n" \
  -s
echo ""
echo ""

# Test 6: Token v těle požadavku
echo "=== Test 6: Token v těle požadavku ==="
curl -X POST "$API_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "api_token": "'$API_TOKEN'",
    "email": "<EMAIL>",
    "name": "Test2",
    "surname": "User2",
    "phone": "+420 608 987 654",
    "lang": "en",
    "flat": "B2-05",
    "text": "Test with token in body"
  }' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s
echo ""
echo ""

echo "=== Testování dokončeno ==="
