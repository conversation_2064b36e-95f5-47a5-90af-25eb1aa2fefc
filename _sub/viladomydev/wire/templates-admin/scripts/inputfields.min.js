var Inputfields={debug:false,processingIfs:false,toggling:false,toggleBehavior:0,defaultDuration:0,init:function($target){InputfieldsInit($target)},toggle:function($inputfield,open,duration,callback){$inputfield=this.inputfield($inputfield);if(!$inputfield.length)return $inputfield;var $header=$inputfield.children(".InputfieldHeader, .ui-widget-header");var $content=$inputfield.children(".InputfieldContent, .ui-widget-content");var $toggleIcon=$header.find(".toggle-icon");var isCollapsed=$inputfield.hasClass("InputfieldStateCollapsed");var isAjax=$inputfield.hasClass("collapsed10")||$inputfield.hasClass("collapsed11");var Inputfields=this;var $siblings=null;if($inputfield.hasClass("InputfieldAjaxLoading"))return $inputfield;if($inputfield.hasClass("InputfieldStateToggling"))return $inputfield;if(!isAjax&&!this.toggling&&$inputfield.hasClass("InputfieldColumnWidth")){var $siblings=Inputfields.getAllInRow($inputfield);if($siblings.length<2)$siblings=null}if(typeof open=="undefined"||open===null)open=isCollapsed;if(typeof duration=="undefined")duration=this.defaultDuration;function completed(){if(Inputfields.toggling===$inputfield.prop("id")){if($siblings&&$siblings.length){$siblings.each(function(){Inputfields.toggle(jQuery(this),open,0)})}setTimeout(function(){Inputfields.toggling=false},100);$siblings=null}if(typeof callback!="undefined")callback($inputfield,open,duration)}function toggled(){if($inputfield.css("overflow")=="hidden")$inputfield.css("overflow","");$toggleIcon.toggleClass($toggleIcon.attr("data-to"));$inputfield.removeClass("InputfieldStateToggling");Inputfields.redraw($inputfield,500);completed()}function opened(){$inputfield.trigger("opened",$inputfield);if($inputfield.hasClass("InputfieldColumnWidth")){$inputfield.children(".InputfieldContent").show()}if($inputfield.prop("id")===Inputfields.toggling&&!$inputfield.hasClass("InputfieldNoFocus")){Inputfields.focus($inputfield)}toggled()}function closed(){if($inputfield.css("overflow")=="hidden")$inputfield.css("overflow","");$inputfield.trigger("closed",$inputfield);if($inputfield.hasClass("InputfieldColumnWidth")){$inputfield.children(".InputfieldContent").hide()}toggled()}if(open&&!$inputfield.is(":visible")){var $tabContent=$inputfield.parents(".InputfieldWrapper").last();if($tabContent.length&&!$tabContent.is(":visible")){var $tabButton=jQuery("#_"+$tabContent.attr("id"));if($tabButton.length){$tabContent.show();setTimeout(function(){$tabButton.trigger("click")},25)}}var $collapsedParent=$inputfield.closest(".InputfieldStateCollapsed:not([id="+$inputfield.attr("id")+"])");if($collapsedParent.length){Inputfields.toggle($collapsedParent,true,duration,function($in){Inputfields.toggle($in,true,duration,callback)})}}if(open&&!isCollapsed){completed();return $inputfield}if(!open&&isCollapsed){completed();return $inputfield}if(isCollapsed&&isAjax&&!$inputfield.hasClass("InputfieldStateWasCollapsed")){$toggleIcon.trigger("click");return $inputfield}if(!this.toggling)this.toggling=$inputfield.prop("id");if(open&&isCollapsed){$inputfield.addClass("InputfieldStateToggling").trigger("openReady",$inputfield);if(duration&&jQuery.ui){$inputfield.toggleClass("InputfieldStateCollapsed",duration,opened)}else{$inputfield.removeClass("InputfieldStateCollapsed");opened()}}else if(!open&&!isCollapsed){$inputfield.addClass("InputfieldStateToggling").trigger("closeReady",$inputfield);if(duration&&jQuery.ui){$inputfield.toggleClass("InputfieldStateCollapsed",duration,closed)}else{$inputfield.addClass("InputfieldStateCollapsed");closed()}}return $inputfield},toggleAll:function($inputfields,open,duration,callback){if(typeof $inputfields==="string")$inputfields=jQuery($inputfields);var Inputfields=this;$($inputfields.get().reverse()).each(function(i,el){Inputfields.toggle($(el),open,duration,callback)});return $inputfields},open:function($inputfield,duration,callback){return this.toggle($inputfield,true,duration)},close:function($inputfield,duration,callback){return this.toggle($inputfield,false,duration)},show:function($inputfield){$inputfield=this.inputfield($inputfield);if(!this.hidden($inputfield))return $inputfield;$inputfield.removeClass("InputfieldStateHidden").show();jQuery(document).trigger("showInputfield",$inputfield);this.redraw(null,50);return $inputfield},hide:function($inputfield){$inputfield=this.inputfield($inputfield);if(this.hidden($inputfield))return $inputfield;$inputfield.addClass("InputfieldStateHidden").hide();jQuery(document).trigger("hideInputfield",$inputfield);this.redraw(null,50);return $inputfield},redraw:function($target,delay){if(typeof delay=="undefined")delay=0;setTimeout(function(){if(typeof $target!="undefined"&&$target&&$target.length){if($target.hasClass("Inputfield"))$target=$target.closest("Inputfields");InputfieldColumnWidths($target)}else{InputfieldColumnWidths()}jQuery(window).trigger("resize")},delay)},reload:function($inputfield,callback){$inputfield=this.inputfield($inputfield);if($inputfield.length){if(typeof callback!="undefined")$inputfield.one("reloaded",callback);$inputfield.trigger("reload")}return $inputfield},focus:function($inputfield,callback){$inputfield=this.inputfield($inputfield);if(!$inputfield.length)return $inputfield;var Inputfields=this;if($inputfield.hasClass("InputfieldStateCollapsed")||!$inputfield.is(":visible")){Inputfields.toggle($inputfield,true,0,function($in,open,duration){Inputfields.focus($in,callback)});return $inputfield}var $input;var focused=false;var showOnly=false;if($inputfield.hasClass("InputfieldNoFocus")){showOnly=true}if(showOnly){$input=jQuery([])}else{$input=$inputfield.find(":input:visible:enabled:not(button):not(.InputfieldNoFocus)").first();if($input.css("position")=="absolute"||$input.is("button"))$input=jQuery([])}if($input.length){var t=$input.attr("type");if($input.is("textarea")||t=="text"||t=="email"||t=="url"||t=="number"){$input.trigger("focus");focused=true}}if(focused){if(typeof callback!="undefined")callback($inputfield)}else if(!this.inView($inputfield)){Inputfields.find($inputfield,false,callback)}return $inputfield},find:function($inputfield,highlight,callback,level){$inputfield=this.inputfield($inputfield);if(!$inputfield.length)return $inputfield;if(typeof highlight=="undefined")highlight=true;if(typeof level=="undefined")level=0;if($inputfield.hasClass("InputfieldStateCollapsed")||!$inputfield.is(":visible")){var hasNoFocus=$inputfield.hasClass("InputfieldNoFocus");if(!hasNoFocus)$inputfield.addClass("InputfieldNoFocus");if($inputfield.hasClass("WireTab")&&!$inputfield.is(":visible"))$inputfield=$inputfield.find(".Inputfield");this.toggle($inputfield,true,0,function($in,open,duration){if(level>9)return;var timeout=level>0?10*level:0;setTimeout(function(){Inputfields.find($inputfield,highlight,callback,level+1)},timeout)});if(!hasNoFocus)$inputfield.removeClass("InputfieldNoFocus");return $inputfield}var completed=function(){if(highlight)Inputfields.highlight($inputfield);if(typeof callback!="undefined")callback($inputfield)};setTimeout(function(){if(false&&Inputfields.inView($inputfield)){completed()}else{var properties={scrollTop:$inputfield.offset().top-10};var options={duration:100,complete:completed};jQuery("html, body").animate(properties,options)}},100);return $inputfield},highlight:function($inputfield,duration,cls){$inputfield=this.inputfield($inputfield);if(typeof cls=="undefined"){cls=$inputfield.hasClass("InputfieldIsHighlight")?"InputfieldIsPrimary":"InputfieldIsHighlight"}if(typeof duration=="undefined"){duration=1e3}$inputfield.addClass(cls);if(duration>0){setTimeout(function(){$inputfield.removeClass(cls)},duration)}return $inputfield},inView:function($inputfield){$inputfield=this.inputfield($inputfield);if(!$inputfield.is(":visible"))return false;var pageTop=jQuery(window).scrollTop();var pageBottom=pageTop+jQuery(window).height();var inputTop=$inputfield.offset().top;var inputBottom=inputTop+$inputfield.height();var inView=inputTop<=pageBottom&&inputBottom>=pageTop;return inView},columnWidth:function($inputfield,value){$inputfield=this.inputfield($inputfield);if(!$inputfield.length)return 0;if(typeof value!="undefined"&&value){if(value>100||value<1)value=100;if(value<100&&!$inputfield.hasClass("InputfieldColumnWidth")){$inputfield.addClass("InputfieldColumnWidth")}var w=this.columnWidth($inputfield);if(w!=value){if(!$inputfield.attr("data-original-width")){$inputfield.attr("data-original-width",w)}$inputfield.attr("data-colwidth",value);$inputfield.trigger("columnWidth",value)}return $inputfield}else{if(!$inputfield.hasClass("InputfieldColumnWidth"))return 100;var pct=$inputfield.attr("data-colwidth");if(typeof pct=="undefined"||!pct.length){var style=$inputfield.attr("style");if(typeof style=="undefined"||!style)return 100;pct=parseInt(style.match(/width:\s*(\d+)/i)[1])}else{pct=parseInt(pct)}if(!$inputfield.attr("data-original-width")){$inputfield.attr("data-original-width",pct)}if(pct<1)pct=100;return pct}},startSpinner:function($inputfield){$inputfield=this.inputfield($inputfield);if(!$inputfield.length)return;var id=$inputfield.attr("id")+"-spinner";var $spinner=$("#"+id);var $header=$inputfield.children(".InputfieldHeader");if(!$spinner.length){$spinner=$("<i class='InputfieldSpinner fa fa-spin fa-spinner'></i>");$spinner.attr("id",id)}$spinner.css({float:"right",marginRight:"30px",marginTop:"3px"});$header.append($spinner.hide());$spinner.fadeIn()},stopSpinner:function($inputfield){$inputfield=this.inputfield($inputfield);if(!$inputfield.length)return;var $spinner=$("#"+$inputfield.attr("id")+"-spinner");if($spinner.length)$spinner.fadeOut("fast",function(){$spinner.remove()})},hidden:function($inputfield){$inputfield=this.inputfield($inputfield);return $inputfield.hasClass("InputfieldStateHidden")},changed:function($inputfield,value){$inputfield=this.inputfield($inputfield);if($inputfield.hasClass("InputfieldIgnoreChanges"))return false;var changed=$inputfield.hasClass("InputfieldStateChanged");if(typeof value=="undefined")return changed;if(value&&!changed){$inputfield.addClass("InputfieldStateChanged").trigger("change");return true}else if(changed){$inputfield.removeClass("InputfieldStateChanged");return false}},name:function($inputfield){$inputfield=this.inputfield($inputfield);if(!$inputfield.length)return"";var name=$inputfield.attr("data-name");if(typeof name!="undefined"&&name&&name.length)return name;name="";var id=$inputfield.prop("id");if(id.indexOf("wrap_Inputfield_")===0){name=id.replace("wrap_Inputfield_","")}else if(id.indexOf("wrap_")===0){name=id.substring(5)}else{var classes=$inputfield.attr("class").split(" ");for(var n=0;n<classes.length;n++){if(classes[n].indexOf("Inputfield_")!==0)continue;name=classes[n].substring(11);break}}if(name.length)$inputfield.attr("data-name",name);return name},label:function($inputfield){$inputfield=this.inputfield($inputfield);if(!$inputfield.length)return"";var label=$inputfield.attr("data-label");if(typeof label!="undefined"&&label&&label.length)return label;var $header=$inputfield.children(".InputfieldHeader");if(!$header.length)return"";label=$header.text();if(label.length)label=label.toString().trim();return label},input:function($inputfield){var $input=jQuery([]);$inputfield=this.inputfield($inputfield);if(!$inputfield.length)return $input;var $header=$inputfield.children(".InputfieldHeader");var attrFor=$header.attr("for");if(attrFor){$input=$inputfield.find(':input[id="'+attrFor+'"]');if($input.length)return $input}var name=this.name($inputfield);if(name.length)$input=$inputfield.find(':input[name="'+name+'"]');return $input},insertBefore:function($insertInputfield,$beforeInputfield){$insertInputfield=this.inputfield($insertInputfield);$beforeInputfield=this.inputfield($beforeInputfield);$beforeInputfield.before($insertInputfield);return $insertInputfield},insertAfter:function($insertInputfield,$afterInputfield){$insertInputfield=this.inputfield($insertInputfield);$afterInputfield=this.inputfield($afterInputfield);$afterInputfield.after($insertInputfield);return $insertInputfield},inputfield:function($inputfield){if(typeof $inputfield=="string"){if($inputfield.indexOf("#")!==0&&$inputfield.indexOf(".")!==0){var $in=jQuery(":input[name="+$inputfield+"]");if(!$in.length)$in=jQuery(":input[id="+$inputfield+"]");if(!$in.length)$in=jQuery(':input[name="'+$inputfield+'[]"]');if(!$in.length)$in=jQuery("#"+$inputfield+".Inputfield");$inputfield=$in}else{$inputfield=jQuery($inputfield)}}if(!$inputfield.length)return jQuery([]);if(!$inputfield.hasClass("Inputfield")){$inputfield=$inputfield.closest(".Inputfield")}return $inputfield},hashAction:function(hash){var pos,action,name;if(hash.indexOf("#")===0)hash=hash.substring(1);pos=hash.indexOf("-");if(pos<3||hash.length<pos+2)return;if(jQuery("#"+hash).length)return;action=hash.substring(0,pos);name=hash.substring(pos+1);if(action==="find"){Inputfields.find(name)}else if(action==="focus"){Inputfields.focus(name)}else if(action==="highlight"){Inputfields.highlight(name)}else{}},getFirstInRow:function($inputfield){$inputfield=this.inputfield($inputfield);if(!$inputfield.length)return $inputfield;if(!$inputfield.hasClass("InputfieldColumnWidth"))return $inputfield;if($inputfield.hasClass("InputfieldColumnWidthFirst"))return $inputfield;var $col=$inputfield;do{$col=$col.prev(".Inputfield");if($col.hasClass("InputfieldColumnWidthFirst"))break}while($col.length&&$col.hasClass("InputfieldColumnWidth"));return $col.hasClass("InputfieldColumnWidthFirst")?$col:$inputfield},getSiblingsInRow:function($inputfield,andSelf,andHidden){$inputfield=this.inputfield($inputfield);if(!$inputfield.length)return $inputfield;if(typeof andSelf==="undefined")andSelf=false;if(typeof andHidden==="undefined")andHidden=false;var pct=this.columnWidth($inputfield);if(pct<1||pct>99)return jQuery([]);var $col=this.getFirstInRow($inputfield);var sel="";while($col.length){if($col.hasClass("InputfieldStateHidden")&&!andHidden){}else if(andSelf||$col.prop("id")!==$inputfield.prop("id")){sel+=(sel.length?",":"")+"#"+$col.prop("id")}$col=$col.next(".InputfieldColumnWidth");if($col.hasClass("InputfieldColumnWidthFirst"))break}return sel.length?jQuery(sel):$inputfield},getAllInRow:function($inputfield,andHidden){if(typeof andHidden==="undefined")andHidden=false;return this.getSiblingsInRow($inputfield,true,andHidden)}};function consoleLog(note){if(Inputfields.debug)console.log(note)}function InputfieldDependencies($target){var $=jQuery;if(Inputfields.processingIfs)return;if(typeof $target=="undefined"){$target=$(".InputfieldForm:not(.InputfieldFormNoDependencies)")}else if($target.hasClass("InputfieldForm")){if($target.hasClass("InputfieldFormNoDependencies"))return}else{if($target.closest(".InputfieldFormNoDependencies").length>0)return}function trimValue(value){value=value.toString().trim();var first=value.substring(0,1);var last=value.substring(value.length-1,value.length);if((first=='"'||first=="'")&&first==last)value=value.substring(1,value.length-1);return value}function trimParseValue(value){return parseValue(trimValue(value))}function extractFieldAndSubfield(field){var subfield="";var dot=field.indexOf(".");if(dot>0){subfield=field.substring(dot+1);field=field.substring(0,dot)}return{field:field,subfield:subfield}}function parseValue(str,str2){if(typeof str==="undefined")return"";str=str.toString().trim();if(str.length>0){if(/^-?\d[\d.]*$/.test(str)){}else{return str}}if(str.length==0){var t=typeof str2;if(t!="undefined"){if(t=="integer")return 0;if(t=="float")return 0;return str}else{return str}}var dot1=str.indexOf(".");var dot2=str.lastIndexOf(".");if(dot1==-1&&/^-?\d+$/.test(str)){return parseInt(str)}if(dot2>-1&&dot1!=dot2){return str}if(/^-?[\d.]+$/.test(str)){return parseFloat(str)}return str}function matchValue(field,operator,value,conditionValue){var matched=0;switch(operator){case"=":if(value===conditionValue)matched++;break;case"!=":if(value!==conditionValue)matched++;break;case">":if(value>conditionValue)matched++;break;case"<":if(value<conditionValue)matched++;break;case">=":if(value>=conditionValue)matched++;break;case"<=":if(value<=conditionValue)matched++;break;case"*=":case"%=":if(value.indexOf(conditionValue)>-1)matched++;break}consoleLog("Field "+field+" - Current value: "+value);consoleLog("Field "+field+" - Matched? "+(matched>0?"YES":"NO"));return matched}function getCheckboxFieldAndValue(condition,conditionField,conditionSubfield){var $field=null;var value;consoleLog("getCheckboxFieldAndValue(see-next-line, "+conditionField+", "+conditionSubfield+")");consoleLog(condition);if(conditionSubfield=="count"||conditionSubfield=="count-checkbox"){consoleLog("Using count checkbox condition");$field=$("#wrap_Inputfield_"+conditionField+" :input");if($field.length){value=$("#wrap_Inputfield_"+conditionField+" :checked").length;condition.subfield="count-checkbox";return{field:$field,value:value,condition:condition}}return null}consoleLog("Using checkbox value or label comparison option");value=[];for(var i=0;i<condition.values.length;i++){var _conditionValue=new String(condition.values[i]);var conditionValue=trimValue(_conditionValue.replace(/\s/g,"_"));var fieldID="#Inputfield_"+conditionField+"_"+conditionValue;$field=$(fieldID);if(!$field.length){fieldID="#"+conditionField+"_"+conditionValue;$field=$(fieldID)}consoleLog("Required condition value: "+conditionValue);if($field.length){var inputType=$field.attr("type");var val="";consoleLog("Found "+inputType+" via value "+fieldID);if($field.is(":checked")){val=$field.val();consoleLog(inputType+" IS checked: "+fieldID)}else if($field.attr("type")=="radio"){consoleLog(inputType+" is NOT checked: "+fieldID);var $checkedField=$field.closest("form").find('input[name="'+$field.attr("name")+'"]:checked');if($checkedField.length){val=$checkedField.val();consoleLog("Checked value is: "+val)}}else{consoleLog(inputType+" is NOT checked: "+fieldID)}if(val.length){consoleLog("Pushing checked value: "+val);value.push(val)}continue}if(conditionValue.length==0||conditionValue.match(/^[0-9]+$/)){consoleLog("Unable to locate checkbox "+fieldID+", skipping");continue}consoleLog("Attempting to find checkbox by label: "+conditionValue);$field=$("#wrap_Inputfield_"+conditionField);var $checkboxes=$field.find("input:checked");for(var cn=0;cn<$checkboxes.length;cn++){var $checkbox=$checkboxes.eq(cn);var $label=$checkbox.closest("label");if($label.length){var label=$label.text().trim();if(label==_conditionValue){consoleLog("Matching checked label found: "+_conditionValue);value.push(label)}else{consoleLog("Matching checked label not found: "+_conditionValue)}}}}if($field)return{field:$field,value:value,condition:condition};return null}function inputfieldChange(conditions,$fieldToShow){Inputfields.processingIfs=true;var fieldNameToShow=$fieldToShow.attr("id").replace(/wrap_Inputfield_/,"");if(Inputfields.debug){consoleLog("-------------------------------------------------------------------");consoleLog('Field "'+fieldNameToShow+'" detected a change to a dependency field! Beginning dependency checks...')}var numVisibilityChanges=0;var show=true;var requiredMatches=0;var notRequiredMatches=0;for(var c=0;c<conditions.length;c++){var condition=conditions[c];if(Inputfields.debug){consoleLog("----");consoleLog("Start Dependency "+c);consoleLog("Condition type: "+condition.type);consoleLog("Field: "+condition.field);if(condition.subfield.length>0)consoleLog("Subfield: "+condition.subfield);consoleLog("Operator: "+condition.operator);consoleLog("Required value: "+condition.value)}var matched=0;for(var fn=0;fn<condition.fields.length;fn++){var fieldAndSubfield=extractFieldAndSubfield(condition.fields[fn]);var conditionField=fieldAndSubfield.field;var conditionSubfield=fieldAndSubfield.subfield;var value=null;var $field=$("#Inputfield_"+conditionField);var hasCheckboxes=false;if($field.length==0)$field=$("#"+conditionField);if($field.length==0){consoleLog("Detected possible checkbox or radio: "+condition.field+condition.operator+condition.value);var fieldAndValue=getCheckboxFieldAndValue(condition,conditionField,conditionSubfield);if(fieldAndValue){$field=fieldAndValue.field;value=fieldAndValue.value;condition=fieldAndValue.condition;hasCheckboxes=true}}if($field.length==0){consoleLog("Unable to locate field: "+conditionField);continue}var $inputfield=$field.closest(".Inputfield");if(value===null){if($field.attr("type")=="checkbox"){value=$field.is(":checked")?$field.val():null}else{value=$field.val()}}var values=[];if(value==null)value="";if(condition.subfield=="count")value=value.length;if(typeof value=="object"){values=jQuery.makeArray(value)}else if(typeof value=="array"){values=value}else if(typeof value=="string"&&$inputfield.hasClass("InputfieldPage")&&value.indexOf(",")>-1&&value.match(/^[,0-9]+$/)){values=value.split(",")}else{values[0]=value}var numMatchesRequired=1;if(condition.operator=="!=")numMatchesRequired=values.length*condition.values.length;if(($field.attr("type")=="checkbox"||$field.attr("type")=="radio")&&!$field.is(":checked")){if($("#Inputfield_"+conditionField+"_0").length==0&&$("#"+conditionField+"_0").length==0){values[1]="0"}}for(var n=0;n<values.length;n++){for(var i=0;i<condition.values.length;i++){var v=parseValue(values[n],condition.values[i]);matched+=matchValue(conditionField,condition.operator,v,condition.values[i])}}if(matched>=numMatchesRequired)break}consoleLog("----");if(condition.type=="show"){if(matched>=numMatchesRequired){}else{show=false}}else if(condition.type=="required"){if(matched>=numMatchesRequired){requiredMatches++}else{notRequiredMatches++}}}var required=requiredMatches>0&&notRequiredMatches==0;if(show){consoleLog('Determined that field "'+fieldNameToShow+'" should be visible.');if(Inputfields.hidden($fieldToShow)){Inputfields.show($fieldToShow);numVisibilityChanges++;consoleLog("Field is now visible.")}else{consoleLog("Field is already visible.")}}else{consoleLog('Determined that field "'+fieldNameToShow+'" should be hidden.');if(Inputfields.hidden($fieldToShow)){consoleLog("Field is already hidden.")}else{Inputfields.hide($fieldToShow);consoleLog("Field is now hidden.");numVisibilityChanges++}if(required){consoleLog("Field is required but cancelling that since it is not visible.");required=false}}if(required&&requiredMatches>0){consoleLog('Determined that field "'+fieldNameToShow+'" should be required.');$fieldToShow.addClass("InputfieldStateRequired").find(":input:visible[type!=hidden]").addClass("required")}else if(!required&&notRequiredMatches>0){consoleLog('Determined that field "'+fieldNameToShow+'" should not be required.');$fieldToShow.removeClass("InputfieldStateRequired").find(":input.required").removeClass("required")}if(numVisibilityChanges>0){consoleLog(numVisibilityChanges+" visibility changes were made.")}Inputfields.processingIfs=false}function setupConditions(conditionType,conditions,$fieldToShow){var selector=$fieldToShow.attr("data-"+conditionType+"-if");if(!selector||selector.length<1){return conditions}selector=$("<div />").html(selector).text();consoleLog("-------------------------------------------------------------------");consoleLog('Analyzing "'+conditionType+'" selector: '+selector);var fieldNameToShow=$fieldToShow.attr("id").replace("wrap_Inputfield_","");var parts=selector.match(/(^|,)([^,]+)/g);for(var n=0;n<parts.length;n++){var part=parts[n];var match=part.match(/^[,\s]*([_.|a-zA-Z0-9]+)(=|!=|<=|>=|<|>|%=)([^,]+),?$/);if(!match)continue;var field=match[1];var operator=match[2];var value=match[3];var subfield="";var fields=[];var values=[];if(field.indexOf("|")>-1){consoleLog("OR field dependency: "+field);fields=field.split("|")}else{fields=[field]}var fieldAndSubfield=extractFieldAndSubfield(field);field=fieldAndSubfield.field;subfield=fieldAndSubfield.subfield;if(Inputfields.debug){consoleLog("Field: "+field);if(subfield.length)consoleLog("Subfield: "+subfield);consoleLog("Operator: "+operator);consoleLog("value: "+value)}if(value.indexOf("|")>-1){consoleLog("OR value dependency: "+value);values=value.split("|");for(var i=0;i<values.length;i++){values[i]=trimParseValue(values[i])}}else{values=[trimParseValue([value])]}var op=operator==="="?"==":operator;var info="if("+fields.join("|")+" "+op+" "+values.join("|")+") "+conditionType+" "+fieldNameToShow;var condition={type:conditionType,field:field,fields:fields,subfield:subfield,operator:operator,value:value,values:values,info:info};conditions[conditions.length]=condition;for(var fn=0;fn<fields.length;fn++){fieldAndSubfield=extractFieldAndSubfield(fields[fn]);var f=fieldAndSubfield.field;var $inputfield=$("#Inputfield_"+f);if($inputfield.length==0){consoleLog("Unable to find inputfield by: #Inputfield_"+f);$inputfield=$("#"+f);if($inputfield.length==0)consoleLog("Unable to find inputfield by: #"+f)}if($inputfield.length==0){$inputfield=$("#wrap_Inputfield_"+f).find(":input");if($inputfield.length==0)consoleLog("Unable to find inputfield by: #wrap_Inputfield_"+f+" :input")}if($inputfield.length==0){$inputfield=$("#wrap_"+f).find(":input");if($inputfield.length==0)consoleLog("Unable to find inputfield by: #wrap_"+f+" :input")}if($inputfield.length){consoleLog("Attaching change event for: "+$inputfield.attr("name"));$inputfield.on("change",function(){inputfieldChange(conditions,$fieldToShow)})}else{consoleLog("Failed to find inputfield, no change event attached")}}}return conditions}function setupDependencyField($fieldToShow){var conditions=[];conditions=setupConditions("show",conditions,$fieldToShow);conditions=setupConditions("required",conditions,$fieldToShow);inputfieldChange(conditions,$fieldToShow)}Inputfields.processingIfs=true;$target.each(function(){$(this).find(".InputfieldStateShowIf, .InputfieldStateRequiredIf").each(function(){setupDependencyField($(this))})});Inputfields.processingIfs=false}function InputfieldColumnWidths($target){var $=jQuery;var hasTarget=true;if(typeof $target=="undefined"){hasTarget=false;$target=$("form.InputfieldForm")}var colspacing=null;var useHeights=null;function getWidth($item){if($item.hasClass("InputfieldStateHidden"))return 0;var pct=$item.attr("data-colwidth");if(typeof pct=="undefined"||!pct.length){var style=$item.attr("style");if(typeof style=="undefined"||!style)return $item.width();pct=parseInt(style.match(/width:\s*(\d+)/i)[1])}else{pct=parseInt(pct)}if(!$item.attr("data-original-width"))$item.attr("data-original-width",pct);return pct}function getOriginalWidth($item){var w=parseInt($item.attr("data-original-width"));if(w==0)w=getWidth($item);return w}function setWidth($item,pct,animate){$item.width(pct+"%");if(animate){$item.css("opacity",.5);$item.animate({opacity:1},150,function(){})}consoleLog("setWidth("+$item.attr("id")+": "+pct+"%")}function getHeight($item){return $item.height()}function setHeight($item,maxColHeight){var h=getHeight($item);consoleLog("setHeight: "+$item.find("label").text()+" >> "+maxColHeight+" ("+h+")");if(h==maxColHeight)return;if($item.hasClass("InputfieldStateCollapsed"))return;var pad=maxColHeight-h;if(pad<0)pad=0;var $container=$item.children(".InputfieldContent, .ui-widget-content");if(pad==0){}else{consoleLog("Adjusting "+$item.attr("id")+" from "+h+" to "+maxColHeight);var $spacer=$("<div class='maxColHeightSpacer'></div>");$container.append($spacer);$spacer.height(pad)}}function updateInputfieldRow($firstItem){var $items=$firstItem.nextUntil(".InputfieldColumnWidthFirst",".InputfieldColumnWidth:not(.InputfieldStateHidden)");var firstItemHidden=$firstItem.hasClass("InputfieldStateHidden");var rowWidth=firstItemHidden?0:getWidth($firstItem);var $item=firstItemHidden?null:$firstItem;var itemWidth=$item==null?0:rowWidth;var numItems=$items.length;var $leadItem;if(firstItemHidden){numItems--;$leadItem=$items.eq(0)}else{$leadItem=$firstItem}if(useHeights){$leadItem.find(".maxColHeightSpacer").remove();$items.find(".maxColHeightSpacer").remove()}var maxRowWidth=100-numItems*colspacing;var maxColHeight=useHeights?getHeight($leadItem):0;$items.removeClass("InputfieldColumnWidthFirstTmp");$items.each(function(){$item=$(this);itemWidth=getWidth($item);rowWidth+=itemWidth;if(useHeights){var h=getHeight($item);if(h>maxColHeight)maxColHeight=h}});if(useHeights){if(Inputfields.debug){var lab=$leadItem.find("label").text();consoleLog("maxColHeight: "+lab+" = "+maxColHeight)}if(maxColHeight>0){setHeight($leadItem,maxColHeight);$items.each(function(){setHeight($(this),maxColHeight)})}}var originalWidth=0;var leftoverWidth=0;if(rowWidth<maxRowWidth){consoleLog("Expand width of row because rowWidth < maxRowWidth ("+rowWidth+" < "+maxRowWidth+")");leftoverWidth=maxRowWidth-rowWidth;consoleLog("leftoverWidth: "+leftoverWidth);itemWidth=itemWidth+leftoverWidth;if($item==null&&!firstItemHidden)$item=$firstItem;if($item){originalWidth=getOriginalWidth($item);if(originalWidth>0&&itemWidth<originalWidth)itemWidth=originalWidth;setWidth($item,itemWidth,true)}}else if(rowWidth>maxRowWidth){consoleLog("Reduce width of row because rowWidth > maxRowWidth ("+rowWidth+" > "+maxRowWidth+")");if(!firstItemHidden)$items=$firstItem.add($items);rowWidth=0;$items.each(function(){$item=$(this);itemWidth=getOriginalWidth($item);if(itemWidth>0)setWidth($item,itemWidth,false);rowWidth+=itemWidth});leftoverWidth=maxRowWidth-rowWidth;itemWidth+=leftoverWidth;originalWidth=getOriginalWidth($item);if(originalWidth>0&&itemWidth<originalWidth)itemWidth=originalWidth;setWidth($item,itemWidth,false)}if(firstItemHidden){$leadItem.addClass("InputfieldColumnWidthFirstTmp")}}var numForms=0;$target.each(function(){var $form=$(this);if(!$form.hasClass("InputfieldForm")){var $_form=$form.closest(".InputfieldForm");if($_form.length)$form=$_form}if($form.hasClass("InputfieldFormNoWidths")){return}colspacing=$form.attr("data-colspacing");if(typeof colspacing=="undefined"){colspacing=1}else{colspacing=parseInt(colspacing)}useHeights=$form.hasClass("InputfieldFormNoHeights")?false:true;$(".Inputfield:not(.InputfieldColumnWidth)",$form).addClass("InputfieldColumnWidthFirst");$(".InputfieldColumnWidthFirst.InputfieldColumnWidth:visible",$form).each(function(){updateInputfieldRow($(this))});numForms++});if(!numForms){}else if(!$("body").hasClass("InputfieldColumnWidthsInit")){$("body").addClass("InputfieldColumnWidthsInit");$(document).on("columnWidth",".Inputfield",function(e,w){setWidth($inputfield,w,$(this));return false})}}function InputfieldFormBeforeUnloadEvent(e){var $=jQuery;var $changes=$(".InputfieldFormConfirm:not(.InputfieldFormSubmitted) .InputfieldStateChanged");if($changes.length==0)return;var msg=$(".InputfieldFormConfirm").eq(0).attr("data-confirm")+"\n";$changes.each(function(){var $header=$(this).find(".InputfieldHeader").eq(0);if($header.length){name=$header.text()}else{name=$(this).attr("data-label");if(!name||!name.length){name=$(this).find(":input").attr("name")}}if(name.length)msg+="\n• "+name.trim()});(e||window.event).returnValue=msg;return msg}function InputfieldFocus($inputfield,completedCallback){Inputfields.focus($inputfield,completedCallback)}function InputfieldToggle($inputfield,open,duration,completedCallback){Inputfields.toggle($inputfield,open,duration,completedCallback)}function InputfieldOpen($inputfield,duration){Inputfields.toggle($inputfield,true,duration)}function InputfieldClose($inputfield,duration){Inputfields.toggle($inputfield,false,duration)}function InputfieldStates($target){var hasTarget=true;var $=jQuery;if(typeof $target=="undefined"){$target=$("body");hasTarget=false}function InputfieldStateAjaxClick($li){function headerHighlightEffect($header,$li){var $spinner=$("<i class='fa fa-spin fa-spinner'></i>");var offset=$header.offset();var interval;var maxRuns=10;var runs=0;var hAdjust=.8;$("body").append($spinner.hide());if($header.is("a")&&$header.closest("ul").hasClass("uk-tab"))hAdjust=.1;$spinner.css({position:"absolute",top:offset.top-($spinner.height()+5),left:offset.left+$header.width()/2+$spinner.width()*hAdjust}).fadeIn();interval=setInterval(function(){if(++runs>maxRuns||!$li.hasClass("InputfieldAjaxLoading")){clearInterval(interval);$spinner.fadeOut("normal",function(){$spinner.remove()})}},500)}var $parent=$li.children(".InputfieldContent").children(".renderInputfieldAjax");var isTab=false;if(!$parent.length){$parent=$li.children(".renderInputfieldAjax");isTab=true}var ajaxURL=$parent.children("input").attr("value");if(typeof ajaxURL=="undefined"||ajaxURL.length<1)return false;var $spinner=null;var $header;if(isTab){$header=$("#_"+$li.attr("id"));headerHighlightEffect($header,$li)}else{$header=$li.children(".InputfieldHeader");$spinner=$("<i class='fa fa-spin fa-spinner'></i>");$spinner.css("margin-left","0.5em");$header.append($spinner)}$li.removeClass("collapsed10 collapsed11").addClass("InputfieldAjaxLoading");$.get(ajaxURL,function(data){$li.removeClass("InputfieldAjaxLoading InputfieldStateCollapsed");var $icon=$li.children(".InputfieldHeader").find(".toggle-icon");if($icon.length)$icon.toggleClass($icon.attr("data-to"));$parent.replaceWith($(data)).hide();$parent.slideDown();var $inputfields=$li.find(".Inputfield");if($inputfields.length){$inputfields.trigger("reloaded",["InputfieldAjaxLoad"]);InputfieldStates($li);InputfieldRequirements($li);InputfieldColumnWidths()}else{$li.trigger("reloaded",["InputfieldAjaxLoad"]);InputfieldColumnWidths()}if($li.closest(".InputfieldFormNoDependencies").length==0){InputfieldDependencies($li.parent())}setTimeout(function(){if($spinner)$spinner.fadeOut("fast",function(){$spinner.remove()});if(isTab){$header.effect("highlight",500)}else if(Inputfields.toggleBehavior<1){$header.trigger("click")}},500)},"html");return true}$(".Inputfield:not(.collapsed9) > .InputfieldHeader, .Inputfield:not(.collapsed9) > .ui-widget-header",$target).addClass("InputfieldStateToggle");var $icon=$(".Inputfields .InputfieldStateCollapsed > .InputfieldHeader i.toggle-icon, .Inputfields .InputfieldStateCollapsed > .ui-widget-header i.toggle-icon",$target);if($icon.length&&typeof $icon.attr("data-to")!=="undefined")$icon.toggleClass($icon.attr("data-to"));if(typeof ProcessWire!="undefined"){var config=ProcessWire.config}if(typeof config!=="undefined"&&config.debug){$(".InputfieldHeader > i.toggle-icon",$target).on("mouseenter",function(){var $label=$(this).parent("label");if($label.length==0)return;var forId=$label.attr("for");if(!forId)forId=$label.parent().attr("id");if(!forId)return;var text=forId.replace(/^Inputfield_|wrap_Inputfield_|wrap_/,"");if(text.length){var $tip=$("<small class='InputfieldNameTip ui-priority-secondary'>&nbsp;"+text+"&nbsp;</small>");$tip.css("float","right");$label.append($tip)}}).on("mouseleave",function(){var $label=$(this).parent("label");if($label.length==0)return;$label.find(".InputfieldNameTip").remove()})}if(hasTarget)return;$(document).on("wiretabclick",function(e,$newTab,$oldTab){if($newTab.hasClass("collapsed10"))InputfieldStateAjaxClick($newTab)});$(document).on("click",".InputfieldStateToggle, .toggle-icon",function(event,data){var $t=$(this);var $li=$t.closest(".Inputfield");var isIcon=$t.hasClass("toggle-icon");var $icon=isIcon?$t:$li.children(".InputfieldHeader, .ui-widget-header").find(".toggle-icon");var isCollapsed=$li.hasClass("InputfieldStateCollapsed");var wasCollapsed=$li.hasClass("InputfieldStateWasCollapsed");var duration=100;var isAjax=$li.hasClass("collapsed10")||$li.hasClass("collapsed11");if(!$li.length)return;if($li.hasClass("InputfieldAjaxLoading"))return false;if($li.hasClass("InputfieldStateToggling"))return false;if(typeof data!="undefined"){if(typeof data.duration!="undefined")duration=data.duration}if(isCollapsed&&isAjax){if(InputfieldStateAjaxClick($li))return false}if(isCollapsed||wasCollapsed||isIcon){$li.addClass("InputfieldStateWasCollapsed");Inputfields.toggle($li,null,duration)}else if(Inputfields.toggleBehavior===1){$icon.trigger("click")}else{if(typeof jQuery.ui!="undefined"){var color1=$icon.css("color");var color2=$li.children(".InputfieldHeader, .ui-widget-header").css("color");$icon.css("color",color2);$icon.effect("pulsate",300,function(){$icon.css("color",color1)})}Inputfields.focus($li)}return false});var $focusInputs=$("input.InputfieldFocusFirst");if(!$focusInputs.length){$focusInputs=$("#content .InputfieldFormFocusFirst:not(.InputfieldFormNoFocus)").find("input[type=text]:enabled").first();if($focusInputs.hasClass("hasDatepicker")||$focusInputs.hasClass("InputfieldNoFocus"))$focusInputs=null}if($focusInputs!==null&&$focusInputs.length){$focusInputs.each(function(){var $t=$(this);if($t.val())return;if($t.offset().top<$(window).height()){window.setTimeout(function(){if($t.is(":visible"))$t.trigger("focus")},250)}})}$(document).on("change",".InputfieldForm :input, .InputfieldForm .Inputfield",function(){var $this=$(this);if($this.hasClass("Inputfield")){if($this.hasClass("InputfieldIgnoreChanges"))return false;$this.addClass("InputfieldStateChanged").trigger("changed");if($this.closest(".InputfieldFormConfirm").length>0)return false}else{if($this.hasClass("InputfieldIgnoreChanges")||$this.closest(".InputfieldIgnoreChanges").length)return false;$this.closest(".Inputfield").addClass("InputfieldStateChanged").trigger("changed")}});$(document).on("submit",".InputfieldFormConfirm",function(){$(this).addClass("InputfieldFormSubmitted")});$(document).on("dragenter",".InputfieldHasUpload.InputfieldStateCollapsed",function(e){var dt=e.originalEvent.dataTransfer;if(dt.types&&(dt.types.indexOf?dt.types.indexOf("Files")!==-1:dt.types.contains("Files"))){InputfieldOpen($(this))}});window.addEventListener("beforeunload",InputfieldFormBeforeUnloadEvent)}function InputfieldIntentions(){var $=jQuery;$(".InputfieldForm").each(function(){var $form=$(this);var numButtons=null;var $input=null;$form.on("submit",function(){if(!$(this).hasClass("nosubmit"))return;if(!$input)return;var $buttons=null;var $inputfields=$input.closest(".Inputfields");do{$buttons=$inputfields.find("input[type=submit]:visible, button[type=submit]:visible");if($buttons.length>0)break;$inputfields=$inputfields.parent().closest(".Inputfields")}while($inputfields.length>0);if($buttons.length>0){var $button=$buttons.eq(0);$("html, body").animate({scrollTop:$button.offset().top},"fast");$button.trigger("focus")}return false}).on("focus","input, select",function(){if(numButtons===null)numButtons=$form.find("input[type=submit], button[type=submit]").length;if(numButtons<2)return;$form.addClass("nosubmit");$input=$(this)}).on("blur","input, select",function(){$form.removeClass("nosubmit")})});if($("input[type=file]").length){$(document).on({dragover:function(){if($(this).is("input[type=file]"))return;return false},drop:function(){if($(this).is("input[type=file]"))return;return false}})}}var InputfieldWindowResizeQueued=false;function InputfieldWindowResizeActions1(){consoleLog("InputfieldWindowResizeActions1()");jQuery(".Inputfield").trigger("resized")}function InputfieldWindowResizeActions2(){consoleLog("InputfieldWindowResizeActions2()");InputfieldColumnWidths();InputfieldWindowResizeQueued=false}function InputfieldRequirements($target){jQuery(":input[required]",$target).on("invalid",function(){var $input=jQuery(this);Inputfields.focus($input)})}function InputfieldReloadEvent(event,extraData){var $t=$(this);var $form=$t.closest("form");var fieldName=$t.attr("id").replace("wrap_Inputfield_","");var fnsx="";var url=$form.attr("action");if(fieldName.indexOf("_repeater")>0){var $repeaterItem=$t.closest(".InputfieldRepeaterItem");var pageID=$repeaterItem.attr("data-page");url=url.replace(/\?id=\d+/,"?id="+pageID);fnsx=$repeaterItem.attr("data-fnsx");fieldName=fieldName.replace(/_repeater\d+$/,"")}url+=url.indexOf("?")>-1?"&":"?";url+="field="+fieldName+"&reloadInputfieldAjax="+fieldName;if(fnsx.length)url+="&fnsx="+fnsx;if(typeof extraData!="undefined"){if(typeof extraData["queryString"]!="undefined"){url+="&"+extraData["queryString"]}}consoleLog("Inputfield reload: "+fieldName);$.get(url,function(data){var id=$t.attr("id");var $content;if(data.indexOf("{")===0){data=JSON.parse(data);console.log(data);$content=""}else{$content=jQuery(data).find("#"+id).children(".InputfieldContent");if(!$content.length&&id.indexOf("_repeater")>-1){id="wrap_Inputfield_"+fieldName;$content=jQuery(data).find("#"+id).children(".InputfieldContent");if(!$content.length){console.log("Unable to find #"+$t.attr("id")+" in response from "+url)}}}if($content.length){$t.children(".InputfieldContent").html($content.html());InputfieldsInit($t);$t.trigger("reloaded",["reload"])}});event.stopPropagation()}function InputfieldsInit($target){InputfieldStates($target);InputfieldDependencies($target);InputfieldRequirements($target);setTimeout(function(){InputfieldColumnWidths()},100)}jQuery(document).ready(function($){InputfieldStates();InputfieldDependencies($(".InputfieldForm:not(.InputfieldFormNoDependencies)"));InputfieldIntentions();setTimeout(function(){InputfieldColumnWidths()},100);var windowResized=function(){if(InputfieldWindowResizeQueued)return;InputfieldWindowResizeQueued=true;setTimeout("InputfieldWindowResizeActions1()",1e3);setTimeout("InputfieldWindowResizeActions2()",2e3)};$(window).on("resize",windowResized);$("ul.WireTabs > li > a").on("click",function(){if(InputfieldWindowResizeQueued)return;InputfieldWindowResizeQueued=true;setTimeout("InputfieldWindowResizeActions1()",250);setTimeout("InputfieldWindowResizeActions2()",500);return true});InputfieldRequirements($(".InputfieldForm"));$(document).on("reload",".Inputfield",InputfieldReloadEvent);if($(".InputfieldForm:not(.InputfieldFormNoWidths)").length){$(document).on("change",".InputfieldColumnWidth :input",function(){InputfieldColumnWidths();setTimeout(InputfieldColumnWidths,300)});$(document).on("AjaxUploadDone",".InputfieldFileList",function(){InputfieldColumnWidths()});$(document).on("heightChanged",".InputfieldColumnWidth",function(){InputfieldColumnWidths()})}if(window.location.hash){Inputfields.hashAction(window.location.hash.substring(1))}});