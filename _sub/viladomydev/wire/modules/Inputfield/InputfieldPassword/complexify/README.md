Complexify
====================

Websites have a responsibility to accurately inform users of password strength, both to better secure data, and to educate about users of what constitutes a good password.

Complexify aims to provide a good measure of password complexity for websites to use both for giving hints to users in the form of strength bars, and for *casually* enforcing a minimum complexity for security reasons.

This plugin only provides client-side validation, and should be combined with some server-side sanity checking. If you want a full-blown Complexify implementation for the server, check out the list of ports.

For more information, a demo, documentation, and the motivation behind Complexify, [visit the website](http://danpalmer.me/jquery-complexify).

### Contributing

Contributions are always welcome! If you're uncertain about something, open a PR and we can discuss. Always feel free to ping me for an update on a merge, reminding me isn't rude, it's helpful.


### Alternative Implementations

Several people have kindly open-sourced their implementations of this algorithm in other languages:

 - Mert <PERSON> [Complexify-Objc](https://github.com/mertdumenci/Complexify-ObjC)
 - <PERSON><PERSON> [node-complexify](https://github.com/kislyuk/node-complexify)
 - <PERSON> [php-complexify](https://github.com/mcrumley/php-complexify/)
 - <PERSON><PERSON><PERSON> [angular-complexify](https://github.com/Kraku/angular-complexify/)

- - -

**This code is distributed under the WTFPL v2 licence.**
