.InputfieldPasswordRow > label {
	position:absolute;
	left:-10000px;
	top:auto;
	width:1px;
	height:1px;
	overflow:hidden;
}

.InputfieldPassword input[disabled=disabled] {
	opacity: 0.6;
}

.InputfieldPassword .pass-confirm > span, 
.InputfieldPassword .pass-scores > span {
	display: none;
}
.InputfieldPassword .pass-confirm > span.on, 
.InputfieldPassword .pass-scores > span.on {
	display: inline;
}

.InputfieldPassword .pass-require-good {
	text-decoration: line-through;
}

.pass-invalid,
.pass-short,
.pass-same, 
.pass-common {
	color: red; 
}

.confirm-no,
.pass-fail {
	color: orangered;
}

.pass-weak {
	color: orangered;
}
.pass-medium {
	color: orangered;
}

.pass-good {
	color: seagreen;
}

.pass-matches .pass-weak,
.pass-matches .pass-medium,
.pass-matches .pass-good,
.pass-matches .pass-excellent {
	color: green;
}

.confirm-yes, 
.pass-excellent {
	color: green;
}

