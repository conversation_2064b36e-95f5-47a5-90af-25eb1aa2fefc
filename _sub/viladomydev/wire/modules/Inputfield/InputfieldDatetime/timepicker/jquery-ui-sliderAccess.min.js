(function($){$.fn.extend({sliderAccess:function(options){options=options||{};options.touchonly=options.touchonly!==undefined?options.touchonly:true;if(options.touchonly===true&&!("ontouchend"in document)){return $(this)}return $(this).each(function(i,obj){var $t=$(this),o=$.extend({},{where:"after",step:$t.slider("option","step"),upIcon:"ui-icon-plus",downIcon:"ui-icon-minus",text:false,upText:"+",downText:"-",buttonset:true,buttonsetTag:"span",isRTL:false},options),$buttons=$("<"+o.buttonsetTag+' class="ui-slider-access">'+'<button data-icon="'+o.downIcon+'" data-step="'+(o.isRTL?o.step:o.step*-1)+'">'+o.downText+"</button>"+'<button data-icon="'+o.upIcon+'" data-step="'+(o.isRTL?o.step*-1:o.step)+'">'+o.upText+"</button>"+"</"+o.buttonsetTag+">");$buttons.children("button").each(function(j,jobj){var $jt=$(this);$jt.button({text:o.text,icons:{primary:$jt.data("icon")}}).on("click",function(e){var step=$jt.data("step"),curr=$t.slider("value"),newval=curr+=step*1,minval=$t.slider("option","min"),maxval=$t.slider("option","max"),slidee=$t.slider("option","slide")||function(){},stope=$t.slider("option","stop")||function(){};e.preventDefault();if(newval<minval||newval>maxval){return}$t.slider("value",newval);slidee.call($t,null,{value:newval});stope.call($t,null,{value:newval})})});$t[o.where]($buttons);if(o.buttonset){$buttons.removeClass("ui-corner-right").removeClass("ui-corner-left").buttonset();$buttons.eq(0).addClass("ui-corner-left");$buttons.eq(1).addClass("ui-corner-right")}var bOuterWidth=$buttons.css({marginLeft:o.where==="after"&&!o.isRTL||o.where==="before"&&o.isRTL?10:0,marginRight:o.where==="before"&&!o.isRTL||o.where==="after"&&o.isRTL?10:0}).outerWidth(true)+5;var tOuterWidth=$t.outerWidth(true);$t.css("display","inline-block").width(tOuterWidth-bOuterWidth)})}})})(jQuery);