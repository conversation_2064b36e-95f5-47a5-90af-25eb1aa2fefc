(function($){$.fn.asmSelect=function(customOptions){var options={listType:"ol",sortable:false,addable:true,deletable:true,highlight:false,fieldset:false,animate:false,addItemTarget:"bottom",hideWhenAdded:false,hideWhenEmpty:false,debugMode:false,jQueryUI:true,hideDeleted:true,deletedOpacity:.5,deletedPrepend:"-",useSelect2:true,removeWhenAdded:false,highlightTag:"<span></span>",sortLabel:'<span class="asmIcon asmIconSort">&#8597;</span>',removeLabel:'<span class="asmIcon asmIconRemove">&times;</span>',highlightAddedLabel:"Added: ",highlightRemovedLabel:"Removed: ",containerClass:"asmContainer",selectClass:"asmSelect",optionDisabledClass:"asmOptionDisabled",listClass:"asmList",listSortableClass:"asmListSortable",listItemClass:"asmListItem",listItemLabelClass:"asmListItemLabel",listItemDescClass:"asmListItemDesc",listItemStatusClass:"asmListItemStatus",listItemHandleClass:"asmListItemHandle",removeClass:"asmListItemRemove",editClass:"asmListItemEdit",highlightClass:"asmHighlight",deletedClass:"asmListItemDeleted",editLink:"",editLabel:'<span class="ui-icon ui-icon-extlink"></span>',editLinkOnlySelected:true,editLinkModal:true,editLinkButtonSelector:"form button.ui-button:visible",optionParentClass:"asmParent",optionParentIcon:"⬇",optionChildAttr:"data-asmParent",optionParentOpenClass:"asmParentOpen",optionChildClass:"asmChild",optionChildIndent:"&nbsp;&nbsp; ",optionParentLabel:"← Click again to make selection"};$.extend(options,customOptions);return this.each(function(index){var $original=$(this);var $container;var $select;var $select2=null;var $ol;var buildingSelect=false;var ieClick=false;var ignoreOriginalChangeEvent=false;var fieldsetCloseItems={};var msie=0;var $highlightSpan=null;function init(){if(options.deletable&&!options.addable)options.hideDeleted=false;$original.find("option[selected]").addClass("asmOriginalSelected");while($("#"+options.containerClass+index).length>0)index++;$select=$("<select></select>").addClass(options.selectClass).addClass($original.attr("class")).attr("name",options.selectClass+index).attr("id",options.selectClass+index);if(!options.addable)$select.hide();$selectRemoved=$("<select></select>");$ol=$("<"+options.listType+"></"+options.listType+">").addClass(options.listClass).attr("id",options.listClass+index);$container=$("<div></div>").addClass(options.containerClass).attr("id",options.containerClass+index);buildSelect();$select.on("change",selectChangeEvent).on("click",selectClickEvent);$original.on("change",originalChangeEvent).wrap($container).before($select).before($ol);if(options.sortable)makeSortable();if(options.fieldset){setupFieldsets();findFieldsetCloseItems($original);$original.on("rebuild",function(e){console.log("asmSelect REBUILD");findFieldsetCloseItems($(this))})}$original.trigger("init");if(options.editLinkModal==="longclick"){$ol.on("longclick","a.asmEditLinkModalLongclick",clickEditLink)}if($select2&&$select2.length){$select2.addClass($select.attr("class")).removeClass("asmSelect").attr("id",$select.attr("id")+"-helper").hide();$select.after($select2)}}function makeSortable(){var fieldsetItems=[];var sortableUpdate=function($ul,e,data){var $option=$("#"+data.item.attr("rel"));var updatedOptionId=$option.attr("id");$ul.children("li").each(function(n){$option=$("#"+$(this).attr("rel"));$original.append($option)});if(updatedOptionId){triggerOriginalChange(updatedOptionId,"sort")}};$ol.sortable({items:"li."+options.listItemClass,axis:"y",cancel:"a.asmEditLinkModalLongclick",update:function(e,data){if(data.item.hasClass("asmFieldsetStart"))return;sortableUpdate(jQuery(this),e,data);$ol.trigger("sorted",[data.item])},start:function(e,data){if(options.jQueryUI)data.item.addClass("ui-state-highlight");if(data.item.hasClass("asmFieldsetStart")){var $next=data.item;var stopName=data.item.find("."+options.listItemLabelClass).text()+"_END";do{if($next.find("."+options.listItemLabelClass).text()==stopName)break;$next=$next.next("li");if($next.length&&!$next.hasClass("ui-sortable-placeholder")){$next.fadeTo(50,.7).slideUp("fast");fieldsetItems.push($next)}}while($next.length)}},stop:function(e,data){if(options.jQueryUI)data.item.removeClass("ui-state-highlight");if(data.item.hasClass("asmFieldsetStart")){var $lastItem=data.item;for(var n=0;n<fieldsetItems.length;n++){var $item=fieldsetItems[n];$lastItem.after($item);$lastItem=$item;$item.slideDown("fast").fadeTo("fast",1)}fieldsetItems=[];setupFieldsets();sortableUpdate(jQuery(this),e,data)}else{setupFieldsets()}}}).addClass(options.listSortableClass)}function selectChangeEvent(e){if(msie>0&&msie<7&&!ieClick)return;var $select=$(this);var $option=$select.children("option:selected");if($highlightSpan&&$highlightSpan.length)$highlightSpan.remove();if(!$option.attr("value").length)return false;if($option.hasClass(options.optionParentClass)){parentOptionSelected($select,$option);e.stopPropagation();return false}var id=$option.slice(0,1).attr("rel");addListItem(id);ieClick=false;triggerOriginalChange(id,"add");if($option.hasClass(options.optionChildClass)){childOptionSelected($select,$option)}}function parentOptionSelected($select,$option){var $sel=$select;var isOpenParent=$option.hasClass(options.optionParentOpenClass);if(options.useSelect2&&!isOpenParent)$sel=getSelect2();var $children=$sel.find("option."+options.optionChildClass+"["+options.optionChildAttr+"='"+$option.attr("value")+"']");var parentHTML=$option.html();var openLabel=" +"+$children.filter(":not(:disabled)").length+" "+options.optionParentIcon;if(isOpenParent){hideSelectOptions($children);parentHTML=parentHTML.replace(/\+\d+ ./,"");$option.removeClass(options.optionParentOpenClass).prop("selected",false)}else{var indent=options.optionChildIndent;if($option.hasClass(options.optionChildClass))indent+=indent;$children.each(function(){var $child=$(this);var childHTML=$child.html();if(childHTML.indexOf(options.optionChildIndent)!==0){$child.html(indent+childHTML)}});showSelectOptions($children,$option);$select.find(":selected").prop("selected",false);if(!$option.hasClass(options.optionChildClass)){$select.find("."+options.optionParentOpenClass).each(function(){$(this).prop("selected",true).trigger("change")})}$option.addClass(options.optionParentOpenClass).prop("selected",true);parentHTML+=openLabel;var highlightOption=options.highlight;options.highlight=true;setHighlight(null,options.optionParentLabel,true);if(!highlightOption)options.highlight=false}$option.html(parentHTML)}function childOptionSelected($select,$option){$select.find("option[value='"+$option.attr(options.optionChildAttr)+"']").prop("selected",true)}function selectClickEvent(){ieClick=true}function originalChangeEvent(e){if(ignoreOriginalChangeEvent){ignoreOriginalChangeEvent=false;return}$select.empty();if(options.useSelect2&&$select2)$select2.empty();$ol.empty();buildSelect();if(typeof $.browser!="undefined"){if($.browser.opera)$ol.hide().fadeIn("fast")}if(options.fieldset)setupFieldsets()}function buildSelect(){buildingSelect=true;var title=$original.attr("title");var numActive=0;if(title===undefined)title="";$select.prepend("<option>"+title+"</option>");$original.children("option").each(function(n){var $t=$(this);var id;if(!$t.attr("id"))$t.attr("id","asm"+index+"option"+n);id=$t.attr("id");if($t.is(":selected")){addListItem(id);addSelectOption(id,true)}else if($t.is(":disabled")){addSelectOption(id,true)}else{numActive++;addSelectOption(id)}});if(!options.debugMode)$original.hide();selectFirstItem();if(options.hideWhenEmpty){if(numActive>0)$select.show();else $select.hide()}buildingSelect=false}function addSelectOption(optionId,disabled){if(typeof disabled=="undefined")disabled=false;var $O=$("#"+optionId);var data_asmParent=options.optionChildAttr;var $option=$("<option>"+$O.html()+"</option>").val($O.val()).attr("rel",optionId);if($O.hasClass(options.optionParentClass)){$option.addClass(options.optionParentClass)}if(disabled)disableSelectOption($option);if($O.attr(data_asmParent)){$option.addClass(options.optionChildClass);$option.attr(data_asmParent,$O.attr(data_asmParent));if(options.useSelect2){var $sel2=getSelect2();$sel2.append($option)}else{hideSelectOptions($option);$select.append($option)}}else{$select.append($option)}}function getSelect2(){if($select2&&$select2.length)return $select2;$select2=$("<select></select>");return $select2}function hideSelectOptions($options){$options.each(function(){var $option=$(this);if(options.useSelect2){var $sel2=getSelect2();$sel2.append($option);if($option.hasClass(options.optionParentOpenClass)){hideSelectOptions($select.children("option."+options.optionChildClass+"["+options.optionChildAttr+'="'+$option.attr("value")+'"]'))}}else{$option.attr("hidden","hidden")}})}function showSelectOptions($options,$afterOption){$options.each(function(){var $option=$(this);if(options.useSelect2){if(typeof $afterOption!="undefined"){$afterOption.after($option);$afterOption=$option}else{$select.append($option)}}else{$option.removeAttr("hidden")}})}function selectFirstItem(){$select.children().first().prop("selected",true)}function disableSelectOption($option){$option.addClass(options.optionDisabledClass).prop("selected",false).prop("disabled",true);if(options.hideWhenEmpty){if($option.siblings("[disabled!=true]").length<2)$select.hide()}if(options.hideWhenAdded)$option.hide();if(msie)$select.hide().show()}function enableSelectOption($option){$option.removeClass(options.optionDisabledClass).prop("disabled",false);if(options.hideWhenEmpty)$select.show();if(options.hideWhenAdded)$option.show();if(msie)$select.hide().show()}function addListItem(optionId){var $O=$("#"+optionId);if(!$O)return;var $removeLink=null;if(options.deletable)$removeLink=$("<a></a>").attr("href","#").addClass(options.removeClass).prepend(options.removeLabel).on("click",function(){dropListItem($(this).parent("li").attr("rel"));return false});var $itemLabel=$("<span></span>").addClass(options.listItemLabelClass);var $itemStatus=$("<span></span>").addClass(options.listItemStatusClass);if($O.attr("data-status"))$itemStatus.html($O.attr("data-status"));var $itemDesc=$("<span></span>").addClass(options.listItemDescClass);if(options.editLink.length>0&&($O.is(":selected")||!options.editLinkOnlySelected)){var $editLink=$("<a></a>").html($O.html()).attr("href",options.editLink.replace(/\{value\}/,$O.val())).append(options.editLabel);if(options.editLinkModal==="longclick"){$editLink.addClass("asmEditLinkModalLongclick")}else if(options.editLinkModal){$editLink.on("click",clickEditLink)}$itemLabel.addClass(options.editClass).append($editLink);if($O.attr("data-desc")){var $editLink2=$("<a></a>").html($O.attr("data-desc")).attr("href",$editLink.attr("href")).append(options.editLabel);$itemDesc.addClass(options.editClass).append($editLink2);if(options.editLinkModal==="longclick"){$editLink2.addClass("asmEditLinkModalLongclick")}else if(options.editLinkModal){$editLink2.on("click",clickEditLink)}}}else{$itemLabel.html($O.html());if($O.attr("data-desc"))$itemDesc.html($O.attr("data-desc"))}var $item=$("<li></li>").attr("rel",optionId).addClass(options.listItemClass).append($itemLabel).append($itemDesc).append($itemStatus);if($removeLink)$item.append($removeLink);$item.hide();if(options.jQueryUI){$item.addClass("ui-state-default").on("mouseenter",function(){$(this).addClass("ui-state-hover").removeClass("ui-state-default")}).on("mouseleave",function(){$(this).addClass("ui-state-default").removeClass("ui-state-hover")});if(options.sortable){if($O.attr("data-handle")){$item.prepend($($O.attr("data-handle")).addClass(options.listItemHandleClass))}else{$item.prepend($(options.sortLabel).addClass(options.listItemHandleClass))}}}if(!buildingSelect){if($O.is(":selected"))return;$O.prop("selected",true)}if(options.addItemTarget=="top"&&!buildingSelect){$ol.prepend($item);if(options.sortable)$original.prepend($O)}else{$ol.append($item);if(options.sortable)$original.append($O)}addListItemShow($item);disableSelectOption($("[rel="+optionId+"]",$select));if(!buildingSelect){setHighlight($item,options.highlightAddedLabel);selectFirstItem();if(options.sortable)$ol.sortable("refresh");if(options.fieldset){var itemName=$O.text();if(itemName.indexOf("_END")>0&&itemName.substring(itemName.length-4)=="_END"){$item.addClass("asmFieldset asmFieldsetEnd")}else{var fieldsetCloseName=itemName+"_END";if(typeof fieldsetCloseItems[fieldsetCloseName]!="undefined"){$item.addClass("asmFieldset asmFieldsetStart");addListItem(fieldsetCloseItems[fieldsetCloseName].attr("id"))}}}}}function addListItemShow($item){if(options.animate&&!buildingSelect){$item.animate({opacity:"show",height:"show"},100,"swing",function(){$item.animate({height:"+=2px"},50,"swing",function(){$item.animate({height:"-=2px"},25,"swing")})})}else{$item.show()}}function dropListItem(optionId,highlightItem){var $O=$("#"+optionId);if(options.hideDeleted||!$O.hasClass("asmOriginalSelected")){if(typeof highlightItem=="undefined")highlightItem=true;$O.prop("selected",false);$item=$ol.children("li[rel="+optionId+"]");dropListItemHide($item);enableSelectOption($("option[rel="+optionId+"]"));if(highlightItem)setHighlight($item,options.highlightRemovedLabel)}else{$item=$ol.children("li[rel="+optionId+"]");var value=$O.attr("value");if(value=="undefined")value=$O.text();if($item.hasClass(options.deletedClass)){$item.removeClass(options.deletedClass);if(options.deletedOpacity!=1)$item.css("opacity",1);$O.attr("value",value.substring(options.deletedPrepend.length))}else{$item.addClass(options.deletedClass);if(options.deletedOpacity!=1)$item.css("opacity",options.deletedOpacity);$O.attr("value",options.deletedPrepend+value)}}triggerOriginalChange(optionId,"drop")}function dropListItemHide($item){if(options.animate&&!buildingSelect){$prevItem=$item.prev("li");$item.animate({opacity:"hide",height:"hide"},100,"linear",function(){$prevItem.animate({height:"-=2px"},50,"swing",function(){$prevItem.animate({height:"+=2px"},100,"swing")});$item.remove()})}else{$item.remove()}}function setHighlight($item,label,remain){if(!options.highlight)return;if(typeof remain=="undefined")remain=false;$select.next("#"+options.highlightClass+index).remove();var $highlight=$(options.highlightTag).hide().addClass(options.highlightClass).attr("id",options.highlightClass+index);if($item){$highlight.html(label+$item.children("."+options.listItemLabelClass).slice(0,1).text())}else{$highlight.html(label)}$select.after($highlight);if(remain){$highlight.fadeIn("fast");$highlightSpan=$highlight}else{$highlight.fadeIn("fast",function(){setTimeout(function(){$highlight.fadeOut("slow",function(){$(this).remove()})},50)})}}function triggerOriginalChange(optionId,type){ignoreOriginalChangeEvent=true;$option=$("#"+optionId);$original.trigger("change",[{option:$option,value:$option.val(),id:optionId,item:$ol.children("[rel="+optionId+"]"),type:type}])}function clickEditLink(e){if(!options.editLinkModal)return true;var $asmItem=$(this).parents("."+options.listItemClass);var href=$(this).attr("href");var $iframe=pwModalWindow(href,{},"medium");$iframe.on("load",function(){setTimeout(function(){iframeLoaded()},100)});var iframeLoaded=function(){var $icontents=$iframe.contents();var buttons=[];var buttonCnt=0;$icontents.find(options.editLinkButtonSelector).each(function(n){var $button=$(this);var label=$button.text();var valid=true;var secondary=$button.is(".ui-priority-secondary");for(var i=0;i<buttonCnt;i++){if(label==buttons[i].text)valid=false}if(valid){buttons[buttonCnt]={text:label,class:secondary?"ui-priority-secondary":"",click:function(){if($button.attr("type")=="submit"){$button.trigger("click");$asmItem.effect("highlight",{},500);var $asmSetStatus=$icontents.find("#"+options.listItemStatusClass);if($asmSetStatus.length==0)$asmSetStatus=$icontents.find(":input."+options.listItemStatusClass);if($asmSetStatus.length>0)$asmItem.find("."+options.listItemStatusClass).html($asmSetStatus.eq(0).val());var $asmSetDesc=$icontents.find("#"+options.listItemDescClass);if($asmSetDesc.length==0)$asmSetDesc=$icontents.find(":input."+options.listItemDescClass);if($asmSetDesc.length>0){$asmSetDesc=$asmSetDesc.eq(0);var asmSetDesc=$("<textarea />").text($asmSetDesc.val()).html();var $desc=$asmItem.find("."+options.listItemDescClass);var $descA=$desc.find("a");if($descA.length>0){$descA.html(asmSetDesc)}else{$desc.html(asmSetDesc)}}}$iframe.dialog("close")}};buttonCnt++}$button.hide()});$iframe.setButtons(buttons)};return false}function setupFieldsets(){$ol.find("span.asmFieldsetIndent").remove();$ol.children("li").children("span."+options.listItemLabelClass).each(function(){var $t=$(this);var label=$t.text();if(label.substring(label.length-4)!="_END")return;label=label.substring(0,label.length-4);var $li=$(this).closest("li."+options.listItemClass);$li.addClass("asmFieldset asmFieldsetEnd");while(1){$li=$li.prev("li."+options.listItemClass);if($li.length<1)break;var $span=$li.children("span."+options.listItemLabelClass);var label2=$span.text();if(label2==label){$li.addClass("asmFieldset asmFieldsetStart");break}$span.prepend($('<span class="asmFieldsetIndent"></span>'))}})}function findFieldsetCloseItems($select){$select.children("option").each(function(){var name=$(this).text();if(name.indexOf("_END")>0&&name.substring(name.length-4)=="_END"){fieldsetCloseItems[name]=$(this)}})}init()})}})(jQuery);