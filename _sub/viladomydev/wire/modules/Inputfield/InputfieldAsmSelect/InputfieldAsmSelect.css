.Inputfields .asmSelect {
	display: inline;
}

.Inputfields .asmList {
	margin-bottom: 0;
}

.Inputfields .asmListItem {
	margin: 0 0 -1px 0; 
	margin-left: 0; 
	line-height: 1.3em;
}

.Inputfields .asmListItem i.fa-trash {
	position: relative;
	top: 2px; 
	right: 3px; 
}

.Inputfields .asmListItem i.asmListItemHandle {
	float: left;
	display: inline;
	margin-right: 7px; 
	position: relative;
	top: 6px; 
	left: 3px; 
	opacity: 0.7;
}
.Inputfields .asmListItem.ui-state-hover i.asmListItemHandle {
	opacity: 1.0;
}

.Inputfields .asmHighlight {
	display: inline;
}

.pw-init .InputfieldAsmSelect select[multiple],
.pw-fouc-fix .InputfieldAsmSelect select[multiple] {
	display: none; 
}

.asmListItemStatus i {
	/* icon */
	margin-right: 0.5em;
}

.asmListItemEdit a > .asmIcon {
	margin-left: 3px; 
	text-decoration: none;
}
