
.pw-content .InputfieldPageAutocomplete {
	p {
		margin: 0;
	}
	ol {
		margin: 0;
		list-style: none;
		
		li {
			/* li item from the html list above */
			position: relative;
			margin: 0 0 1px 0;
			padding-left: 0;
			list-style: none;
			width: 100%;
			line-height: 1em;
			display: block;
			cursor: move;
			
			.itemSort {
				display: inline;
				float: left;
				margin-right: 7px;
				position: relative;
				top: 3px;
				left: 3px;
				opacity: 0.7; 
			}
		}
		li:last-child {
			margin-bottom: 0.5em;
		}
	}
	
	input.no_list {
		/* some right padding for remove icon placement */
		padding-right: 22px;
	}

}

.InputfieldPageAutocomplete {
	ol {
		li {
			.itemLabel {
				/* this is a span that surrounds the text in the item, except for the remove link */
				display: block;
				padding: 3px 0 3px 5px;
				cursor: move;
			}
			
			.itemRemove {
				/* the remove link in each list item */
				position: absolute;
				right: 3px;
				top: 0;
			}
			
			.itemValue {
				display: none;
			}
			
			i.fa-trash {
				position: relative;
				top: 2px;
				right: 3px;
			}
		}
		
		li.itemTemplate {
			display: none !important;
		}
	}
	
	input[type=text] {
		/* some left padding for icon placement */
		width: 100%;
		padding-left: 18px;
	}

	p {
		position: relative;
		margin-bottom: 0;
		
		i.fa {
			/* icon placement */
			position: absolute;
			top: 0.5em;
			left: 1px;
			opacity: 0.5;
		}
		
		i.fa.InputfieldPageAutocompleteRemove {
			/* remove icon, for no_list mode */
			left: auto;
			right: 3px;
			cursor: pointer;
			&:hover {
				opacity: 1.0;
			}
		}
	}
	
	.InputfieldPageAdd {
		/* hide .InputfieldPageAdd since autocomplete uses it's own input for this */
		display: none;
	}
}

.ui-autocomplete {
	/* enforce scrolling, plus add a little shadow for better distinction from background */
	max-height: 230px; 
	overflow-y: auto;
	overflow-x: hidden; 
	box-shadow: 0 1px 2px 2px rgba(0,0,0,0.2); 
}

.InputfieldPageAutocompleteNote {
	/* Optional note with contextual helper tips */
	display: none; 
}

