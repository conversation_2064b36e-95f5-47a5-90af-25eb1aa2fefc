.InputfieldSelector .selector-list {
	margin-left: 0;
	padding-left: 0;
	list-style: none;
	margin-bottom: 0.5em; 
}
.InputfieldSelector .selector-list li {
	margin-left: 0;
	padding-left: 0;
	list-style: none;
}

.InputfieldSelector .selector-row {
	margin: 0;
	border-top: 1px solid #eee;
	padding: 0.5em 0 0.5em 0.25em;
	line-height: inherit;
}

.InputfieldSelector .selector-row:last-child {
	/* first non template row */
	border-bottom: 1px solid #eee;
}

.InputfieldSelector .selector-template-row {
	display: none; 
}

.InputfieldSelector .selector-row i.fa, 
.InputfieldSelector .selector-row select, 
.InputfieldSelector .selector-row input {
	float: left; 
}
.InputfieldSelector .selector-row select, 
.InputfieldSelector .selector-row input {
	display: block;
	float: left; 
}
.InputfieldSelector .selector-row i.fa {
	margin-top: 0.15em; 
	
}

.InputfieldSelector .selector-row a.add-or-field {
	float: left;
	width: 2%; 
	margin-right: 1%; 
}

.InputfieldSelector .selector-row select.select-field {
	/* 30 */
	width: 39%; 
	margin-right: 1%; 
	margin-bottom: 0;
	float: left;
}

.InputfieldSelector .selector-row .subfield {
	display: none; 
}

.InputfieldSelector .selector-row .opval {
}

.InputfieldSelector .selector-row .opval select.select-value,
.InputfieldSelector .selector-row .opval input.input-value,
.InputfieldSelector .selector-row .opval input.input-value-autocomplete {
	/* 50 */
	width: 39%; 
	margin-left: 0;
	margin-right: 0;
}


.InputfieldSelector .selector-row .opval select.select-operator {
	/* 15 */
	width: 14%; 
	margin-right: 1%; 
}

.InputfieldSelector .selector-row .delete-row {
	/* 5 */
	float: right; 
	text-align: right; 
	width: 3%; 
	display: block;
}
.InputfieldSelector .selector-row .delete-row i.fa {
	float: right;
}

.InputfieldSelector .selector-row.has-subfield .subfield {
	/* 20 */
	display: block;
	float: left; 
	width: 19%; 
	margin-right: 1%; 
}
.InputfieldSelector .selector-row.has-subfield select.select-field {
	/* 20 */
	width: 19%; 
}

.InputfieldSelector .selector-row.has-subfield select.select-subfield {
	width: 100%; 
}
.InputfieldSelector .selector-row.has-subfield .opval select.select-value,
.InputfieldSelector .selector-row.has-subfield .opval input.input-value,
.InputfieldSelector .selector-row.has-subfield .opval input.input-value-autocomplete {
	/*
	width: 37%; 
	*/
}

.InputfieldSelector .selector-row .opval input.input-or {
	margin-left: 0.5%; 
	display: none; 
}
body.AdminThemeDefault .InputfieldSelector .selector-row .opval input.input-or,
body.AdminThemeReno .InputfieldSelector .selector-row .opval input.input-or {
	width: 1.5%; 
}

.InputfieldSelector .selector-row.has-or-value .opval input.input-or,
.InputfieldSelector .selector-row.has-or-field .opval input.input-or {
	display: block;
}

.InputfieldSelector .selector-row.has-or-field .opval .selector-operator,
.InputfieldSelector .selector-row.has-or-field .opval .input-value,
.InputfieldSelector .selector-row.has-or-field .select-operator,
.InputfieldSelector .selector-row.has-or-value .select-field, 
.InputfieldSelector .selector-row.has-or-value .select-operator, 
.InputfieldSelector .selector-row.has-or-value .subfield {
	/*
	opacity: 0.3; 
	*/
}

.InputfieldSelector a.selector-add {
	width: 50%; 
	float: left;
	display: block;
	clear: both; 
}

.InputfieldSelector .selector-counter {
	width: 50%; 
	float: right; 
	margin-bottom: 1em;
	text-align: right; 
}

.InputfieldSelector .selector-preview {
	clear:both;
	text-align: right;
	margin-bottom: 0;
}

.InputfieldSelector .selector-preview code {
	font-size: 14px; 
}

.InputfieldSelector .selector-counter-disabled,
.InputfieldSelector .selector-preview-disabled {
	display: none; 
}

.InputfieldSelector .or-notes {
	display: none; 
	margin-bottom: 0;
}

.InputfieldSelector .or-notes,
.InputfieldSelector .notes {
	clear: both;
	margin-top: 1em;
}

.InputfieldSelector .selector-preview-disabled + input + .detail,
.InputfieldSelector .selector-preview-disabled + input + .notes {
	position: relative;
	top: 0.5em;
}

