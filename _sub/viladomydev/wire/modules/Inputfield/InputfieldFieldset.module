<?php namespace ProcessWire;

/**
 * Fieldset Inputfield
 *
 * ProcessWire 3.x, Copyright 2022 by <PERSON>
 * https://processwire.com
 *
 * @property string|int $defaultValue
 * @property array|string $options Get or set options, array of [value => label], or use options string.
 * @property array $optionAttributes
 * @property bool $valueAddOption If value attr set from API (only) that is not an option, add it as an option? (default=false) 3.0.171+
 *
 */

class InputfieldFieldset extends InputfieldWrapper {

	/**
	 * Get module info
	 * 
	 * @return array
	 * 
	 */
	public static function getModuleInfo() {
		return array(
			'title' => __('Fieldset', __FILE__), // Module Title
			'summary' => __('Groups one or more fields together in a container', __FILE__), // Module Summary
			'version' => 101,
			'permanent' => true, 
		);
	}

	/**
	 * Render
	 * 
	 * @return string
	 * 
	 */
	public function ___render() {
		// Note the extra "\n" is required in order to prevent <PERSON><PERSON>field<PERSON>rap<PERSON> from 
		// skipping over an empty fieldset. Empty fieldsets are used by InputfieldRepeater
		// for their description/label, and possibly others use it the same way. 
		return parent::___render() . "\n";
	}
	
}

