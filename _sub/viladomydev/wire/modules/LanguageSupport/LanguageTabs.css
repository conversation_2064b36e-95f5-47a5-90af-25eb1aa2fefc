.body.LanguageTabsJqueryUI .pw-content .langTabsContainer .InputfieldError,.body.LanguageTabsJqueryUI #content .langTabsContainer .InputfieldError{margin-bottom:0}.langTabs.ui-tabs{position:relative;padding:0}.langTabs.ui-tabs .ui-tabs-nav{margin-bottom:0}.langTabs.ui-tabs .ui-tabs-nav.ui-widget-header{padding:2px 3px 0;border-bottom:none}.langTabs.ui-tabs .ui-tabs-nav li{margin:0 4px 0 0}.langTabs.ui-tabs .ui-tabs-nav li a{padding:.3em .7em;font-weight:bold;outline:none;font-size:.8461538462em}.langTabs.ui-tabs .ui-tabs-nav li.ui-state-active.ui-state-default{border:1px solid #fff;padding-bottom:0}.langTabs.ui-tabs .ui-tabs-nav li.ui-state-active{background:#fff}.langTabs.ui-tabs .ui-tabs-nav li.ui-state-active a{cursor:default;background:#fff;margin:0}.langTabs.ui-tabs .ui-tabs-panel{padding:.9em;border-top:none}.langTabs.ui-tabs .ui-tabs-panel .LanguageSupportLabel{display:none}.langTabsToggle{float:right;margin-right:1em;cursor:pointer}.InputfieldStateCollapsed .langTabsToggle{display:none}.langTabEmpty a{opacity:.7;font-weight:normal !important}.langTabs>ul.ui-tabs{display:none}.hasLangTabs .langTabs>ul.ui-tabs{display:block}.InputfieldImage .InputfieldFileLink+.langTabsContainer{margin-top:.5em}.hadLanguageSupport>.InputfieldContent>.LanguageSupport{margin-bottom:0}.langTabsHidden,.hadLanguageSupport .LanguageSupportLabel{display:none}.langTabsNote{position:absolute;top:5px;right:1em;display:none}
