<?php namespace ProcessWire;

require_once(dirname(__FILE__) . '/FieldtypeLanguageInterface.php'); 

/**
 * Multi-language capable textarea field
 *
 * ProcessWire 3.x, Copyright 2023 by <PERSON>
 * https://processwire.com
 *
 *
 */

class FieldtypeTextareaLanguage extends FieldtypeTextarea implements FieldtypeLanguageInterface {

	public static function getModuleInfo() {
		return array(
			'title' => 'Textarea (Multi-language)',
			'version' => 100,
			'summary' => 'Field that stores a multiple lines of text in multiple languages',
			'requires' => array('LanguageSupportFields')
		);
	}

	/**
	 * Sanitize value for storage
	 * 
	 * @param Page $page
	 * @param Field $field
	 * @param LanguagesPageFieldValue|string|array $value
	 * @return LanguagesPageFieldValue
	 *
	 */
	public function sanitizeValue(Page $page, Field $field, $value) {
		
		if($value instanceof LanguagesPageFieldValue) return $value;
		
		if(is_array($value)) $value = reset($value); 
		
		$value = (string) $value;
		$template = $page->template;
		$pageValue = $page->data($field->name); 
		
		if(!$pageValue instanceof LanguagesPageFieldValue) { // string, array or null
			$pageValue = new LanguagesPageFieldValue($page, $field, $value);
		}
		
		if($template && $template->noLang) {
			$languages = $this->wire()->languages;
			$pageValue->setLanguageValue($languages->getDefault()->id, $value);
		} else {
			$language = $this->wire()->user->language;
			if($language) $pageValue->setLanguageValue($language->id, $value);
		}
		
		return $pageValue; 
	}

	/**
	 * Return the database schema in specified format
	 * 
	 * @param Field $field
	 * @return array
	 *
	 */
	public function getDatabaseSchema(Field $field) {
		$schema = parent::getDatabaseSchema($field);
		/** @var LanguageSupport $languageSupport */
		$languageSupport = $this->wire()->modules->get('LanguageSupport'); 
		foreach($languageSupport->otherLanguagePageIDs as $languageID) {
			// $schema['data' . $languageID] = $schema['data'];
			$schema['data' . $languageID] = 'mediumtext';
			$schema['keys']["data{$languageID}"] = "FULLTEXT KEY `data{$languageID}` (`data{$languageID}`)";
		}
		return $schema;
	}

	/**
	 * Format value for output, basically typcasting to a string and sending to textformatters from FieldtypeTextarea
	 * 
	 * @param Page $page
	 * @param Field $field
	 * @param string|LanguagesPageFieldValue $value
	 * @return string
	 *
	 */
	public function formatValue(Page $page, Field $field, $value) {
		return parent::formatValue($page, $field, (string) $value); 
	}

	/**
	 * Given a value, return an portable version of it as array
	 *
	 * @param Page $page
	 * @param Field $field
	 * @param string|int|float|array|object|null $value
	 * @param array $options Optional settings to shape the exported value, if needed.
	 * @return string|float|int|array
	 *
	 */
	public function ___exportValue(Page $page, Field $field, $value, array $options = array()) {
		// in this method we are delegating the exportValue() to FieldtypeTextLanguage::exportValue
		// but we want to make sure it has textarea sleepValue, so we pass sleepValue in the $options
		if(!isset($options['sleepValue'])) $options['sleepValue'] = $this->sleepValue($page, $field, $value);
		$exportValue = $this->wire()->fieldtypes->get('FieldtypeTextLanguage')->___exportValue($page, $field, $value, $options);
		return $exportValue; 
	}

	/**
	 * Given an export value, import it to a LanguagesPageFieldValue 
	 * 
	 * @param Page $page
	 * @param Field $field
	 * @param array $value
	 * @param array $options
	 * @return LanguagesPageFieldValue
	 * 
	 */
	public function ___importValue(Page $page, Field $field, $value, array $options = array()) {
		$languages = $this->wire()->languages;
	
		/** @var LanguagesPageFieldValue $importValue */
		$importValue = $this->wire()->fieldtypes->get('FieldtypeTextLanguage')->___importValue($page, $field, $value, $options);
		
		foreach($languages as $language) {
			$languageValue = $importValue->getLanguageValue($language->id); 
			$languageValue = parent::___importValue($page, $field, $languageValue, $options); 
			$importValue->setLanguageValue($language->id, $languageValue);
		}
		
		return $importValue; 
	}
}
