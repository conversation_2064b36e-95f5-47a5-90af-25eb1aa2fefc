<?php namespace ProcessWire;

/**
 * ProcessWire Strip Tags Module
 *
 * Strips tags, except those allowed in the module configuration.
 * 
 * ProcessWire 3.x, Copyright 2016 by <PERSON>
 * https://processwire.com
 *
 *
 */

class TextformatterStripTags extends Textformatter implements ConfigurableModule {

	protected $data = array(
		'allowedTags' => '',
	);

	public static function getModuleInfo() {
		return array(
			'title' => 'Strip Markup Tags', 
			'version' => 100, 
			'summary' => "Strips HTML/XHTML Markup Tags", 
		); 
	}

	public function format(&$str) {
		$str = strip_tags($str, $this->data['allowedTags']); 
	}

	public function getModuleConfigInputfields(array $data) {
		$inputfields = $this->wire(new InputfieldWrapper());
		$name = "allowedTags";
		if(!isset($data[$name])) $data[$name] = '';
		/** @var InputfieldText $f */
		$f = $this->wire()->modules->get('InputfieldText');
		$f->attr('name', $name);
		$f->attr('value', $data[$name]);
		$f->label = 'Allowed Markup Tags';
		$f->description = 
			"Enter any markup tags that are allowed, i.e. `<strong><em>`. " . 
			"Note that this does not strip attributes for any tags you allow. " . 
			"As a result, you should not allow *any* tags unless all potential users are trusted.";
		$inputfields->append($f);
		return $inputfields;
	}

	public function __get($key) {
		return isset($this->data[$key]) ? $this->data[$key] : null;
	}

	public function __set($key, $value) {
		$this->data[$key] = $value; 
	}

}
