<?php namespace ProcessWire;

/**
 * ProcessWire Newlines to <br /> Textformatter
 *
 * Inserts <br /> tags automatically into newlines
 * 
 * ProcessWire 3.x, Copyright 2016 by <PERSON>
 * https://processwire.com
 *
 *
 */

class TextformatterNewlineBR extends Textformatter {

	public static function getModuleInfo() {
		return array(
			'title' => 'Newlines to XHTML Line Breaks', 
			'version' => 100, 
			'summary' => "Converts newlines to XHTML line break <br /> tags. ", 
		); 
	}

	public function format(&$str) {
		$str = nl2br(trim($str));
	}
}
