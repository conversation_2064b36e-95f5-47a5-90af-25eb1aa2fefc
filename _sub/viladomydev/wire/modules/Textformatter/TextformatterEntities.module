<?php namespace ProcessWire;

/**
 * ProcessWire Entities Textformatter
 *
 * Formats text with PHP's htmlspecialchars() function.
 * 
 * ProcessWire 3.x, Copyright 2023 by <PERSON>
 * https://processwire.com
 *
 *
 */

class TextformatterEntities extends Textformatter {

	public static function getModuleInfo() {
		return array(
			'title' => __('HTML Entity Encoder (htmlspecialchars)', __FILE__), // Module Title
			'summary' => __("Entity encode ampersands, quotes (single and double) and greater-than/less-than signs using htmlspecialchars(str, ENT_QUOTES). It is recommended that you use this on all text/textarea fields except those using a rich text editor or a markup language like Markdown.", __FILE__), // Module Summary
			'version' => 100, 
		); 
	}
	
	protected $charset = '';

	public function format(&$str) {
		if($this->charset === '') $this->charset = $this->wire()->config->dbCharset;
		if(stripos($this->charset, 'utf8') === 0) {
			$str = htmlspecialchars($str, ENT_QUOTES, 'UTF-8');
		} else {
			$str = htmlspecialchars($str, ENT_QUOTES);
		}
	}
}
