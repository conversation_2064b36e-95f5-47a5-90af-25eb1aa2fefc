# Leads API Documentation

## Endpoint
**POST** `/leads-api/`

## Authentication
API používá fixní token pro autentifikaci. Token můžete poslat dvěma způsoby:

1. **V těle požadavku** jako `api_token`
2. **V HTTP hlavičce** jako `X-API-Token`

**API Token:** `viladomy_api_2024_secure_token_xyz789`

## Požadovaná pole

| Pole | Typ | Popis | Povinné |
|------|-----|-------|---------|
| `api_token` | string | API token pro autentifikaci | Ano |
| `email` | string | Platná emailová adresa | Ano |
| `name` | string | Jméno | Ano |
| `surname` | string | Příjmení | Ano |
| `phone` | string | Telefonní <PERSON>lo (formát: +420 777 123 456) | Ano |
| `lang` | string | Jazyk ("cs" nebo "en") | Ano |
| `flat` | string | Označení bytu/jednotky | Ano |
| `question` nebo `text` | string | Dotaz/zpráva | Ne |

## Příklady cURL volání

### 1. Základní volání s JSON daty

```bash
curl -X POST https://dev.viladomyumlyna.cz//leads-api/ \
  -H "Content-Type: application/json" \
  -H "X-API-Token: viladomy_api_2024_secure_token_xyz789" \
  -d '{
    "email": "<EMAIL>",
    "name": "Jan",
    "surname": "Novák",
    "phone": "+420 777 123 456",
    "lang": "cs",
    "flat": "A1-01",
    "question": "Mám zájem o více informací o tomto bytě."
  }'
```

### 2. Volání s tokenem v těle požadavku

```bash
curl -X POST https://dev.viladomyumlyna.cz/leads-api/ \
  -H "Content-Type: application/json" \
  -d '{
    "api_token": "viladomy_api_2024_secure_token_xyz789",
    "email": "<EMAIL>",
    "name": "Marie",
    "surname": "Svoboda",
    "phone": "+420 608 987 654",
    "lang": "cs",
    "flat": "B2-05",
    "text": "Chtěla bych si domluvit prohlídku."
  }'
```

### 3. Volání s form-data

```bash
curl -X POST https://dev.viladomyumlyna.cz//leads-api/ \
  -H "X-API-Token: viladomy_api_2024_secure_token_xyz789" \
  -F "email=<EMAIL>" \
  -F "name=John" \
  -F "surname=Smith" \
  -F "phone=+420 777 555 333" \
  -F "lang=en" \
  -F "flat=C3-12" \
  -F "question=I would like to schedule a viewing."
```

### 4. Minimální volání (bez otázky)

```bash
curl -X POST https://dev.viladomyumlyna.cz//leads-api/ \
  -H "Content-Type: application/json" \
  -H "X-API-Token: viladomy_api_2024_secure_token_xyz789" \
  -d '{
    "email": "<EMAIL>",
    "name": "Test",
    "surname": "User",
    "phone": "+420 123 456 789",
    "lang": "cs",
    "flat": "-"
  }'
```

## Odpovědi API

### Úspěšná odpověď (HTTP 201)

```json
{
  "success": true,
  "message": "Lead successfully saved",
  "lead_id": 123,
  "data": {
    "email": "<EMAIL>",
    "name": "Jan",
    "surname": "Novák",
    "phone": "+420 777 123 456",
    "lang": "cs",
    "flat": "A1-01",
    "text": "Mám zájem o více informací o tomto bytě.",
    "created": 1704067200
  }
}
```

### Chyba validace (HTTP 400)

```json
{
  "success": false,
  "error": "Validation failed",
  "code": "VALIDATION_ERROR",
  "errors": [
    {
      "message": "Valid email address is required",
      "field": "email",
      "code": "REQUIRED_FIELD"
    }
  ]
}
```

### Chyba autentifikace (HTTP 401)

```json
{
  "success": false,
  "error": "Invalid or missing API token",
  "code": "UNAUTHORIZED"
}
```

### Chyba metody (HTTP 405)

```json
{
  "success": false,
  "error": "Method not allowed. Only POST requests are accepted.",
  "code": "METHOD_NOT_ALLOWED"
}
```

### Databázová chyba (HTTP 500)

```json
{
  "success": false,
  "error": "Database error occurred",
  "code": "DATABASE_ERROR",
  "message": "Internal server error"
}
```

## Testování

Pro testování API můžete použít následující příkaz:

```bash
# Test s platným tokenem
curl -X POST https://dev.viladomyumlyna.cz//leads-api/ \
  -H "Content-Type: application/json" \
  -H "X-API-Token: viladomy_api_2024_secure_token_xyz789" \
  -d '{
    "email": "<EMAIL>",
    "name": "Test",
    "surname": "User",
    "phone": "+420 123 456 789",
    "lang": "cs",
    "flat": "TEST-01",
    "question": "Test API call"
  }' \
  -v
```

## Bezpečnostní poznámky

1. **API token** je fixní a měl by být udržován v tajnosti
2. Všechny požadavky jsou **logovány** (pokud je zapnut debug režim)
3. API **validuje** všechna vstupní data
4. **Pouze POST** požadavky jsou povoleny
5. Podporuje jak **JSON** tak **form-data** formáty
