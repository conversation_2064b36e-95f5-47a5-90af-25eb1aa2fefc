<?php

/**
 * Alternativní script pro vytvoření API stránky přímo přes databázi
 * Použijte tento script, pokud create_api_page.php nefunguje
 */

// Zobraz všechny PHP chyby
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);

echo "=== Direct Database API Page Creator ===\n";

// Načti konfiguraci ProcessWire
if (!file_exists('./site/config.php')) {
    echo "CHYBA: Soubor config.php nenalezen.\n";
    exit(1);
}

require_once('./site/config.php');

// Získej databázové údaje z konfigurace
$dbHost = $config->dbHost ?? '127.0.0.1';
$dbName = $config->dbName ?? '';
$dbUser = $config->dbUser ?? '';
$dbPass = $config->dbPass ?? '';

echo "Databáze: $dbHost / $dbName\n";
echo "Uživatel: $dbUser\n\n";

if (empty($dbName) || empty($dbUser)) {
    echo "CHYBA: Databázová konfigurace není kompletní.\n";
    exit(1);
}

try {
    // Připoj se k databázi
    $pdo = new PDO("mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4", $dbUser, $dbPass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✓ Připojení k databázi úspěšné.\n";

    // Zkontroluj, jestli fieldgroup existuje
    $stmt = $pdo->prepare("SELECT id, name FROM fieldgroups WHERE name = 'leads-api'");
    $stmt->execute();
    $fieldgroup = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$fieldgroup) {
        echo "Vytvářím fieldgroup 'leads-api'...\n";

        // Vytvoř fieldgroup
        $stmt = $pdo->prepare("INSERT INTO fieldgroups (name, created, modified) VALUES ('leads-api', UNIX_TIMESTAMP(), UNIX_TIMESTAMP())");
        $stmt->execute();
        $fieldgroupId = $pdo->lastInsertId();

        // Přidej title field do fieldgroup (field ID 1 je obvykle title)
        try {
            $stmt = $pdo->prepare("INSERT INTO fieldgroups_fields (fieldgroups_id, fields_id, sort) VALUES (:fg_id, 1, 0)");
            $stmt->execute([':fg_id' => $fieldgroupId]);
            echo "✓ Title field přidáno do fieldgroup.\n";
        } catch (Exception $e) {
            echo "! Title field se nepodařilo přidat: " . $e->getMessage() . "\n";
        }

        echo "✓ Fieldgroup 'leads-api' vytvořena (ID: $fieldgroupId)\n";
    } else {
        $fieldgroupId = $fieldgroup['id'];
        echo "✓ Fieldgroup 'leads-api' už existuje (ID: $fieldgroupId)\n";
    }

    // Zkontroluj, jestli template existuje
    $stmt = $pdo->prepare("SELECT id, name FROM templates WHERE name = 'leads-api'");
    $stmt->execute();
    $template = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$template) {
        echo "Vytvářím template 'leads-api'...\n";

        // Vytvoř template s fieldgroup
        $stmt = $pdo->prepare("INSERT INTO templates (name, filename, fieldgroups_id, created, modified) VALUES ('leads-api', 'leads-api.php', :fg_id, UNIX_TIMESTAMP(), UNIX_TIMESTAMP())");
        $stmt->execute([':fg_id' => $fieldgroupId]);
        $templateId = $pdo->lastInsertId();

        echo "✓ Template 'leads-api' vytvořen (ID: $templateId)\n";
    } else {
        $templateId = $template['id'];
        echo "✓ Template 'leads-api' už existuje (ID: $templateId)\n";
    }

    // Zkontroluj, jestli stránka existuje
    $stmt = $pdo->prepare("SELECT id, name FROM pages WHERE name = 'leads-api'");
    $stmt->execute();
    $page = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$page) {
        echo "Vytvářím stránku 'leads-api'...\n";
        
        // Najdi root stránku (parent_id = 0 nebo id = 1)
        $stmt = $pdo->prepare("SELECT id FROM pages WHERE parent_id = 0 OR id = 1 LIMIT 1");
        $stmt->execute();
        $rootPage = $stmt->fetch(PDO::FETCH_ASSOC);
        $parentId = $rootPage ? $rootPage['id'] : 1;
        
        // Vytvoř stránku
        $stmt = $pdo->prepare("
            INSERT INTO pages (parent_id, templates_id, name, status, created, modified, sort) 
            VALUES (:parent_id, :template_id, 'leads-api', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0)
        ");
        $stmt->execute([
            ':parent_id' => $parentId,
            ':template_id' => $templateId
        ]);
        $pageId = $pdo->lastInsertId();
        
        echo "✓ Stránka 'leads-api' vytvořena (ID: $pageId)\n";
        
        // Přidej title do field_data_title (pokud existuje)
        try {
            $stmt = $pdo->prepare("INSERT INTO field_title (pages_id, data) VALUES (:page_id, 'Leads API Endpoint')");
            $stmt->execute([':page_id' => $pageId]);
            echo "✓ Title přidán.\n";
        } catch (Exception $e) {
            echo "! Title se nepodařilo přidat (možná tabulka neexistuje): " . $e->getMessage() . "\n";
        }
        
    } else {
        echo "✓ Stránka 'leads-api' už existuje (ID: {$page['id']})\n";
    }

    echo "\n=== Úspěch! ===\n";
    echo "API endpoint je dostupný na:\n";
    echo "POST https://viladomyumlyna.cz/leads-api/\n";
    echo "API token: viladomy_api_2024_secure_token_xyz789\n";

} catch (PDOException $e) {
    echo "CHYBA databáze: " . $e->getMessage() . "\n";
    echo "Kód chyby: " . $e->getCode() . "\n";
    exit(1);
} catch (Exception $e) {
    echo "CHYBA: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n=== Hotovo ===\n";
