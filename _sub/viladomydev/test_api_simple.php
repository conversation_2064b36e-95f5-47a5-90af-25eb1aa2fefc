<?php

/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON> test script pro Leads API
 * Spustit z příkazové řádky: php test_api_simple.php
 */

$API_URL = 'https://viladomyumlyna.cz/leads-api/';
$API_TOKEN = 'viladomy_api_2024_secure_token_xyz789';

echo "=== Test Leads API ===\n";
echo "URL: $API_URL\n";
echo "Token: $API_TOKEN\n\n";

// Test data
$testData = [
    'email' => '<EMAIL>',
    'name' => 'Test',
    'surname' => 'User',
    'phone' => '+420 777 123 456',
    'lang' => 'cs',
    'flat' => 'A1-01',
    'question' => 'Test API call from PHP script'
];

// Připrav cURL požadavek
$ch = curl_init();

curl_setopt_array($ch, [
    CURLOPT_URL => $API_URL,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => json_encode($testData),
    CURLOPT_HTTPHEADER => [
        'Content-Type: application/json',
        'X-API-Token: ' . $API_TOKEN
    ],
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_SSL_VERIFYPEER => false, // Pro testování
    CURLOPT_TIMEOUT => 30
]);

echo "Odesílám test požadavek...\n";
echo "Data: " . json_encode($testData, JSON_PRETTY_PRINT) . "\n\n";

// Pošli požadavek
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);

curl_close($ch);

// Zobraz výsledek
if ($error) {
    echo "cURL Error: $error\n";
} else {
    echo "HTTP Status: $httpCode\n";
    echo "Response:\n";
    
    // Pokus se dekódovat JSON odpověď
    $jsonResponse = json_decode($response, true);
    if ($jsonResponse) {
        echo json_encode($jsonResponse, JSON_PRETTY_PRINT) . "\n";
    } else {
        echo $response . "\n";
    }
}

echo "\n=== Test dokončen ===\n";
