<?php

/**
 * Debug script pro kontrolu prostředí
 */

// Zobraz všechny PHP chyby
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);

echo "=== Environment Debug ===\n";
echo "PHP Version: " . PHP_VERSION . "\n";
echo "Current Directory: " . getcwd() . "\n";
echo "Script Path: " . __FILE__ . "\n";
echo "Memory Limit: " . ini_get('memory_limit') . "\n";
echo "Max Execution Time: " . ini_get('max_execution_time') . "\n";

// Zkontroluj soubory
echo "\n=== File Check ===\n";
$files_to_check = [
    './index.php',
    './site/config.php',
    './wire/core/ProcessWire.php',
    './site/templates/leads-api.php'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        echo "✓ $file exists\n";
    } else {
        echo "✗ $file NOT FOUND\n";
    }
}

// Zkontroluj permissions
echo "\n=== Permissions Check ===\n";
$dirs_to_check = [
    './site/templates/',
    './site/assets/',
    './site/'
];

foreach ($dirs_to_check as $dir) {
    if (is_dir($dir)) {
        $perms = substr(sprintf('%o', fileperms($dir)), -4);
        echo "Directory $dir: $perms\n";
    } else {
        echo "Directory $dir: NOT FOUND\n";
    }
}

// Test základního PHP
echo "\n=== PHP Functions Test ===\n";
$functions_to_check = [
    'json_encode',
    'json_decode',
    'curl_init',
    'file_get_contents',
    'class_exists'
];

foreach ($functions_to_check as $func) {
    if (function_exists($func)) {
        echo "✓ $func available\n";
    } else {
        echo "✗ $func NOT AVAILABLE\n";
    }
}

// Test ProcessWire načtení (bez spuštění)
echo "\n=== ProcessWire Test ===\n";
try {
    if (file_exists('./index.php')) {
        $content = file_get_contents('./index.php');
        if (strpos($content, 'ProcessWire') !== false) {
            echo "✓ index.php obsahuje ProcessWire\n";
        } else {
            echo "✗ index.php neobsahuje ProcessWire\n";
        }
    }
    
    if (file_exists('./site/config.php')) {
        $content = file_get_contents('./site/config.php');
        if (strpos($content, 'dbHost') !== false) {
            echo "✓ config.php obsahuje databázovou konfiguraci\n";
        } else {
            echo "✗ config.php neobsahuje databázovou konfiguraci\n";
        }
    }
    
} catch (Exception $e) {
    echo "Chyba při testování ProcessWire: " . $e->getMessage() . "\n";
}

// Test API template souboru
echo "\n=== API Template Test ===\n";
if (file_exists('./site/templates/leads-api.php')) {
    $content = file_get_contents('./site/templates/leads-api.php');
    if (strpos($content, 'viladomy_api_2024_secure_token_xyz789') !== false) {
        echo "✓ leads-api.php obsahuje správný API token\n";
    } else {
        echo "✗ leads-api.php neobsahuje správný API token\n";
    }
    
    if (strpos($content, 'INSERT INTO leads') !== false) {
        echo "✓ leads-api.php obsahuje SQL dotaz\n";
    } else {
        echo "✗ leads-api.php neobsahuje SQL dotaz\n";
    }
} else {
    echo "✗ leads-api.php neexistuje\n";
}

echo "\n=== Debug Complete ===\n";
