<?php

/**
 * Script pro vytvoření API stránky v ProcessWire
 * Spustit z příkazové řádky: php create_api_page.php
 */

// Include ProcessWire bootstrap
try {
    include('./index.php');
} catch (Exception $e) {
    echo "Chyba při načítání ProcessWire: " . $e->getMessage() . "\n";
    echo "Ujistěte se, že spouštíte script z root adresáře ProcessWire.\n";
    exit(1);
}

// Zkontroluj, jestli už stránka existuje
$existingPage = wire('pages')->get('/leads-api/');
if ($existingPage->id) {
    echo "Stránka /leads-api/ už existuje (ID: {$existingPage->id})\n";
    echo "URL: https://dev.viladomyumlyna.cz/leads-api/\n";
    exit;
}

// Zkontroluj, jestli template existuje
try {
    $template = wire('templates')->get('leads-api');
    if (!$template->id) {
        echo "Vytvářím template 'leads-api'...\n";

        // Vytvoř template
        $template = new Template();
        $template->name = 'leads-api';
        $template->filename = 'leads-api.php';
        $template->save();

        echo "Template 'leads-api' vytvořen (ID: {$template->id})\n";
    } else {
        echo "Template 'leads-api' už existuje (ID: {$template->id})\n";
    }
} catch (Exception $e) {
    echo "Chyba při práci s template: " . $e->getMessage() . "\n";
    exit(1);
}

// Vytvoř stránku
echo "Vytvářím stránku /leads-api/...\n";

try {
    $page = new Page();
    $page->template = $template;
    $page->parent = wire('pages')->get('/'); // Root page
    $page->name = 'leads-api';
    $page->title = 'Leads API Endpoint';
    $page->save();

    echo "Stránka vytvořena úspěšně!\n";
    echo "ID: {$page->id}\n";
    echo "URL: https://dev.viladomyumlyna.cz/leads-api/\n";
    echo "Template: {$page->template->name}\n";

    echo "\nAPI endpoint je nyní dostupný na:\n";
    echo "POST https://dev.viladomyumlyna.cz/leads-api/\n";
    echo "\nAPI token: viladomy_api_2024_secure_token_xyz789\n";
} catch (Exception $e) {
    echo "Chyba při vytváření stránky: " . $e->getMessage() . "\n";

    // Zobraz více detailů o chybě
    if (method_exists($e, 'getTraceAsString')) {
        echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    }

    // Zkus zobrazit chyby z ProcessWire
    if (function_exists('wire')) {
        $notices = wire('notices');
        if ($notices && count($notices)) {
            echo "\nProcessWire notices:\n";
            foreach ($notices as $notice) {
                echo "- " . $notice->text . "\n";
            }
        }
    }

    exit(1);
}
