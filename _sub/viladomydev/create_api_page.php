<?php


/**
 * Script pro vytvoření API stránky v ProcessWire
 * Spustit z příkazové řádky: php create_api_page.php
 */

// Zobraz všechny PHP chyby
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);

echo "=== ProcessWire API Page Creator ===\n";
echo "PHP Version: " . PHP_VERSION . "\n";
echo "Current Directory: " . getcwd() . "\n";
echo "Script Path: " . __FILE__ . "\n";
echo "Memory Limit: " . ini_get('memory_limit') . "\n";
echo "Max Execution Time: " . ini_get('max_execution_time') . "\n";
echo "=====================================\n\n";

// Include ProcessWire bootstrap
echo "Pokouším se načíst ProcessWire...\n";
try {
    if (!file_exists('./index.php')) {
        throw new Exception("Soubor index.php nenalezen. Ujistěte se, že spouštíte script z root adresáře ProcessWire.");
    }

    // Načti ProcessWire bootstrap
    require_once('./index.php');
    echo "ProcessWire bootstrap načten.\n";

    // Zkontroluj různé způsoby přístupu k ProcessWire
    $pw = null;

    if (function_exists('wire')) {
        echo "✓ Funkce wire() je dostupná.\n";
        $pw = wire();
    } elseif (class_exists('ProcessWire\\ProcessWire')) {
        echo "✓ ProcessWire třída je dostupná.\n";
        $pw = \ProcessWire\ProcessWire::getCurrentInstance();
    } elseif (isset($wire)) {
        echo "✓ Globální \$wire proměnná je dostupná.\n";
        $pw = $wire;
    } else {
        throw new Exception("ProcessWire není správně inicializován. Žádný způsob přístupu k API není dostupný.");
    }

    if (!$pw) {
        throw new Exception("ProcessWire instance není dostupná.");
    }

    echo "ProcessWire instance získána úspěšně.\n";

    // Zkontroluj databázové připojení
    $database = $pw->database ?? $pw->wire('database');
    if (!$database) {
        throw new Exception("Databázové připojení není dostupné.");
    }

    echo "Databázové připojení je aktivní.\n";

} catch (Exception $e) {
    echo "CHYBA při načítání ProcessWire: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
} catch (Error $e) {
    echo "PHP ERROR při načítání ProcessWire: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . " Line: " . $e->getLine() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}

// Zkontroluj, jestli už stránka existuje
$pages = $pw->pages ?? $pw->wire('pages');
$existingPage = $pages->get('/leads-api/');
if ($existingPage && $existingPage->id) {
    echo "Stránka /leads-api/ už existuje (ID: {$existingPage->id})\n";
    echo "URL: https://dev.viladomyumlyna.cz/leads-api/\n";
    exit;
}

// Zkontroluj, jestli template existuje
try {
    $templates = $pw->templates ?? $pw->wire('templates');
    $template = $templates->get('leads-api');

    if (!$template || !$template->id) {
        echo "Vytvářím template 'leads-api'...\n";

        // Nejprve vytvoř fieldgroup
        $fieldgroups = $pw->fieldgroups ?? $pw->wire('fieldgroups');
        $fieldgroup = new \ProcessWire\Fieldgroup();
        $fieldgroup->name = 'leads-api';

        // Přidej základní pole title (každý template ho potřebuje)
        $fields = $pw->fields ?? $pw->wire('fields');
        $titleField = $fields->get('title');
        if ($titleField) {
            $fieldgroup->add($titleField);
        }

        $fieldgroup->save();
        echo "Fieldgroup 'leads-api' vytvořena (ID: {$fieldgroup->id})\n";

        // Nyní vytvoř template s fieldgroup
        $template = new \ProcessWire\Template();
        $template->name = 'leads-api';
        $template->filename = 'leads-api.php';
        $template->fieldgroup = $fieldgroup;
        $template->save();

        echo "Template 'leads-api' vytvořen (ID: {$template->id})\n";
    } else {
        echo "Template 'leads-api' už existuje (ID: {$template->id})\n";
    }
} catch (Exception $e) {
    echo "CHYBA při práci s template: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . " Line: " . $e->getLine() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
} catch (Error $e) {
    echo "PHP ERROR při práci s template: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . " Line: " . $e->getLine() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}

// Vytvoř stránku
echo "Vytvářím stránku /leads-api/...\n";

try {
    $page = new \ProcessWire\Page();
    $page->template = $template;
    $page->parent = $pages->get('/'); // Root page
    $page->name = 'leads-api';
    $page->title = 'Leads API Endpoint';
    $page->save();

    echo "Stránka vytvořena úspěšně!\n";
    echo "ID: {$page->id}\n";
    echo "URL: https://dev.viladomyumlyna.cz/leads-api/\n";
    echo "Template: {$page->template->name}\n";

    echo "\nAPI endpoint je nyní dostupný na:\n";
    echo "POST https://dev.viladomyumlyna.cz/leads-api/\n";
    echo "\nAPI token: viladomy_api_2024_secure_token_xyz789\n";
} catch (Exception $e) {
    echo "CHYBA při vytváření stránky: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . " Line: " . $e->getLine() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";

    // Zkus zobrazit chyby z ProcessWire
    if (isset($pw)) {
        $notices = $pw->notices ?? ($pw->wire ? $pw->wire('notices') : null);
        if ($notices && count($notices)) {
            echo "\nProcessWire notices:\n";
            foreach ($notices as $notice) {
                echo "- " . $notice->text . " (type: " . $notice->className . ")\n";
            }
        }

        // Zkus zobrazit session notices
        $session = $pw->session ?? ($pw->wire ? $pw->wire('session') : null);
        if ($session) {
            $sessionNotices = $session->getFor('notices');
            if ($sessionNotices && count($sessionNotices)) {
                echo "\nSession notices:\n";
                foreach ($sessionNotices as $notice) {
                    echo "- " . (is_string($notice) ? $notice : print_r($notice, true)) . "\n";
                }
            }
        }
    }

    exit(1);
} catch (Error $e) {
    echo "PHP ERROR při vytváření stránky: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . " Line: " . $e->getLine() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
