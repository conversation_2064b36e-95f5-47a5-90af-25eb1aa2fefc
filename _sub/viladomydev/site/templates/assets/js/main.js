$(document).ready(function () {

    /** AOS - init */
    AOS.init({
        once: true,
    });

    /** Open nav - bind */
    $(".j-open-nav").on("click", function () {
        $(".header__m-nav-wrap").toggleClass("opened");
        $(".header__nav-wrap").toggleClass("opened-light");
        $(".burger").toggleClass("opened");
        $("body").toggleClass("prevent-scroll");
    });

    /** J-scroll */
    $(".j-scroll").on("click", function (e) {
        if (this.hash !== "") {
            e.preventDefault();
            var hash = this.hash;

            $("html, body").animate(
                {
                    scrollTop: $(hash).offset().top,
                },
                500
            );
        }
    });

    //Contact form AJAX
    $("#contact-form").submit(function (ev) {
        // Prevent the form from actually submitting
        ev.preventDefault();
        var url = $(this).attr("action");
        var form = $(this);
        var par = $(this).attr("id");
        var parId = "#" + par;
        var formSuccess = $(parId + " #message-success");
        var formMessage = $(parId + " #message-other");

        // Send it to the server
        $.ajax({
            url: url,
            method: "POST",
            data: form.serialize(),
            success: function (data) {
                $(".form__error, .form__success").remove();

                if (data.type == "success") {
                    form[0].reset();
                    formSuccess.html('<div class="form__success">' + data.message + "</div>");

                    var formJson = form.serialize();

                    if (formJson.includes("flat=-")) {
                        dataLayer.push({ event: "Kontakt" });
                        console.log("Kontakt");
                    } else {
                        dataLayer.push({ event: "Poptavka" });
                        console.log("Poptavka");
                    }
                } else {
                    messages = data.errors;
                    console.log(data.errors);
                    messages.forEach((msg) => {
                        var elem = $(parId + " #message-" + msg.errortype);
                        elem.html('<div class="form__error">' + msg.message + "</div>");
                    });
                }
            },
            error: function () {
                $(".form__error, .form__success").remove();
                formMessage.html('<p class="form__error">Unexpected error. Try it again later.</p>');
            },
        });
    });

    //Contact form AJAX
    $("#contact-form-modal").submit(function (ev) {
        // Prevent the form from actually submitting
        ev.preventDefault();
        var url = $(this).attr("action");
        var form = $(this);
        var par = $(this).attr("id");
        var parId = "#" + par;
        var formSuccess = $(parId + " #message-success-modal");
        var formMessage = $(parId + " #message-other-modal");

        // Send it to the server
        $.ajax({
            url: url,
            method: "POST",
            data: form.serialize(),
            success: function (data) {
                $(".form__error, .form__success").remove();

                if (data.type == "success") {
                    form[0].reset();
                    formSuccess.html('<div class="form__success">' + data.message + "</div>");

                    var formJson = form.serialize();

                    if (formJson.includes("flat=-")) {
                        dataLayer.push({ event: "Kontakt" });
                        console.log("Kontakt");
                    } else {
                        dataLayer.push({ event: "Poptavka" });
                        console.log("Poptavka");
                    }
                } else {
                    messages = data.errors;
                    console.log(data.errors);
                    messages.forEach((msg) => {
                        var elem = $(parId + " #message-modal-" + msg.errortype);
                        elem.html('<div class="form__error">' + msg.message + "</div>");
                    });
                }
            },
            error: function () {
                $(".form__error, .form__success").remove();
                formMessage.html('<p class="form__error">Unexpected error. Try it again later.</p>');
            },
        });
    });

    /**modal */

    // Get the modal
    var modal = document.getElementById("formModal");

    // When the user clicks the button, open the modal
    $(".j-open-modal").on("click", function (e) {
        if (modal) {
            modal.style.display = "block";
        }
        var name = $(this).data("flat");
        $(".j-form-flat-name").text(name);
        $(".j-form-flat-input").val(name);
    });

    const swiper = new Swiper(".swiper", {
        // Optional parameters
        direction: "horizontal",
        loop: true,

        // Navigation arrows
        navigation: {
            nextEl: ".swiper-button-next-custom",
            prevEl: ".swiper-button-prev-custom",
        },
    });

    // Download PDF event
    document.querySelectorAll(".j-download-link").forEach(function (item) {
        item.addEventListener("click", function (e) {
            var path = item.getAttribute("href");
            var url = "https://viladomyumlyna.cz" + path;

            dataLayer.push({
                event: "file_download",
                eventModel: {
                    link_id: "",
                    link_url: url,
                    link_text: "Stáhnout",
                    file_name: path,
                    file_extension: "pdf",
                },
            });
        });
    });
});

function adjustPickImageSize() {
    const containers = document.querySelectorAll(".j-pick-container");

    containers.forEach((container) => {
        const image = container.querySelector(".j-pick-image");
        const svg = container.querySelector(".j-pick-svg");

        const containerRatio = container.offsetWidth / container.offsetHeight;
        const imageRatio = image.naturalWidth / image.naturalHeight;

        let newWidth, newHeight;

        if (containerRatio > imageRatio) {
            // Nadřazený prvek je širší než obrázek
            newWidth = container.offsetWidth;
            newHeight = newWidth / imageRatio;
        } else {
            // Nadřazený prvek je užší než obrázek
            newHeight = container.offsetHeight;
            newWidth = newHeight * imageRatio;
        }

        // Nastavit rozměry obrázku
        image.style.width = `${newWidth}px`;
        image.style.height = `${newHeight}px`;
        image.style.objectFit = "cover";

        // Nastavit rozměry SVG
        if (svg) {
            svg.style.width = `${newWidth}px`;
            svg.style.height = `${newHeight}px`;
        }

        container.classList.add("loaded");
    });
}

// Tippy js - Standards
tippy(".standards__hotspot", {
    content(reference) {
        return reference.getAttribute("data-tippy-content");
    },
    arrow: false,
    offset: [0, -40],
    animation: "fade",
    theme: "custom-light",
    placement: "top",
    maxWidth: 370,
});

// Spustit funkci při načtení stránky a při změně velikosti okna
window.addEventListener("load", adjustPickImageSize);
window.addEventListener("resize", adjustPickImageSize);

// Accordion - STANDARDS
document.querySelectorAll(".standards__m-accord-header").forEach((header) => {
    header.addEventListener("click", () => {
        const item = header.parentElement;
        const allItems = document.querySelectorAll(".standards__m-accord-item");

        allItems.forEach((i) => {
            if (i !== item) i.classList.remove("active");
        });

        item.classList.toggle("active");
    });
});

// Accordion - STANDARDS
document.querySelectorAll(".standards__m-hotspot").forEach((hotspot) => {
    hotspot.addEventListener("click", () => {
        const targetId = hotspot.dataset.target;
        const targetItem = document.querySelector(`.standards__m-accord-item[data-id="${targetId}"]`);

        if (targetItem) {
            // Zavřít všechny
            document.querySelectorAll(".standards__m-accord-item").forEach((item) => item.classList.remove("active"));

            // Otevřít konkrétní
            targetItem.classList.add("active");

            // Scroll to the target item
            targetItem.scrollIntoView({ behavior: "smooth" });
        }
    });
});








// LOCA - columns












document.addEventListener('DOMContentLoaded', () => {
  const columns = document.querySelectorAll('.loca__column');
  const details = document.querySelectorAll('.loca__detail-wrap');

  columns.forEach((column, index) => {
    column.addEventListener('click', () => {
      const isActive = column.classList.contains('active');

      // Zavřít vše
      columns.forEach(col => col.classList.remove('active', 'hidden'));
      details.forEach(det => det.classList.remove('visible'));

      if (!isActive) {
        // Aktivuj tento sloupec a odpovídající detail
        column.classList.add('active');
        columns.forEach(col => {
          if (col !== column) col.classList.add('hidden');
        });

        const detailToShow = details[index];
        if (detailToShow) detailToShow.classList.add('visible');

        // ⬆️ Posuň stránku nahoru (hladce)
        window.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      }
    });
  });
});
