<?php

namespace ProcessWire;

// Debug API endpoint - jedno<PERSON><PERSON><PERSON> test
header('Content-Type: application/json');

$debug = [
    'status' => 'API endpoint is working',
    'timestamp' => date('Y-m-d H:i:s'),
    'method' => $_SERVER['REQUEST_METHOD'] ?? 'UNKNOWN',
    'headers' => getallheaders(),
    'get_data' => $_GET,
    'post_data' => $_POST,
    'raw_input' => file_get_contents('php://input'),
    'server_info' => [
        'PHP_VERSION' => PHP_VERSION,
        'SERVER_SOFTWARE' => $_SERVER['SERVER_SOFTWARE'] ?? 'UNKNOWN',
        'REQUEST_URI' => $_SERVER['REQUEST_URI'] ?? 'UNKNOWN'
    ]
];

// Pokus se dekódovat JSON input
$json_input = file_get_contents('php://input');
if ($json_input) {
    $decoded = json_decode($json_input, true);
    if ($decoded) {
        $debug['json_input'] = $decoded;
    } else {
        $debug['json_error'] = json_last_error_msg();
    }
}

http_response_code(200);
echo json_encode($debug, JSON_PRETTY_PRINT);
exit;
