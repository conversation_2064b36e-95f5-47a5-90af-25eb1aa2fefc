<?php

namespace ProcessWire;

$errors = array();
$success = false;
$data = null;

$honeypotInput = $input->post->namemad;
$langInput = $sanitizer->text($input->post->lang);
$flatInput = $sanitizer->text($input->post->flat);
$mailInput = $sanitizer->email($input->post->mail);
$nameInput = $sanitizer->text($input->post->name);
$surnameInput = $sanitizer->text($input->post->surname);
$questionInput = $sanitizer->text($input->post->question);
$mobileInput = $sanitizer->match($input->post->mobile, '/^[0-9 +()*#]+$/');
$oouInput = $sanitizer->checkbox($input->post->oou, $yes = true, $no = false);
$marketingInput = $sanitizer->checkbox($input->post->marketing, $yes = true, $no = false);


if ($input->post->action == 'send') {

  // validate CSRF token
  if (!$session->CSRF->hasValidToken()) {
    // $error["message"] = __("CSFR token error.");
    // $error["errortype"] = "other";
    // array_push($errors, $error);
  }

  // Validate honeypot
  if ($honeypotInput != "") {
    $error["message"] = __("Honeypot error.");
    $error["errortype"] = "other";
    array_push($errors, $error);
  }

  // Validate lang
  if (empty($langInput)) {
    $error["message"] = __("Pole jazyk je povinné.");
    $error["errortype"] = "other";
    array_push($errors, $error);
  } else if ($langInput != "en" && $langInput != "cs") {
    $error["message"] = __("Pole jazyk error.");
    $error["errortype"] = "other";
    array_push($errors, $error);
  }

  // Validate flat
  if (empty($flatInput)) {
    $error["message"] = __("Error.");
    $error["errortype"] = "other";
    array_push($errors, $error);
  } 

  // Validate mail
  if (empty($mailInput)) {
    $error["message"] = __("Zadejte prosím platnou e-mailovou adresu");
    $error["errortype"] = "mail";
    array_push($errors, $error);
  }

  // Validate name
  if (empty($nameInput)) {
    $error["message"] = __("Vyplňte prosím své jméno");
    $error["errortype"] = "name";
    array_push($errors, $error);
  }

  // Validate surname
  if (empty($surnameInput)) {
    $error["message"] = __("Vyplňte prosím své příjmení");
    $error["errortype"] = "surname";
    array_push($errors, $error);
  }
//    $error["message"] = __("BAF");
//    $error["errortype"] = "surname";
//    array_push($errors, $error);
  // Skip question validation
  if (empty($questionInput)) {
  }

  // Validate mobile
  if (empty($mobileInput)) {
    $error["message"] = __("Zadejte prosím telefonní číslo ve správném formátu např.") . " +420&nbsp;777&nbsp;123&nbsp;456";
    $error["errortype"] = "mobile";
    array_push($errors, $error);
  }

  // Validate oou
  if (empty($oouInput) || !$oouInput) {
    $error["message"] = __("Pro přihlášení k odběru novinek musíte souhlasit se zpracováním osobních údajů.");
    $error["errortype"] = "oou";
    array_push($errors, $error);
  }

  // SKIP Validate marketing
  if (empty($marketingInput) || !$marketingInput) {
    // $error["message"] = __("Pro přihlášení k odběru novinek musíte souhlasit se zasíláním marketingových sdělení.");
    // $error["errortype"] = "marketing";
    // array_push($errors, $error);
  }

  // If not errors, send to db
  if (!$errors) {
    if (strlen($mailInput)) {

      // Save lead to database
      try {
        $database = wire('database');
        $sql = "INSERT INTO leads (email, name, surname, phone, lang, flat, text, created)
                VALUES (:email, :name, :surname, :phone, :lang, :flat, :text, :created)";

        $query = $database->prepare($sql);
        $query->bindValue(':email', $mailInput, \PDO::PARAM_STR);
        $query->bindValue(':name', $nameInput, \PDO::PARAM_STR);
        $query->bindValue(':surname', $surnameInput, \PDO::PARAM_STR);
        $query->bindValue(':phone', $mobileInput, \PDO::PARAM_STR);
        $query->bindValue(':lang', $langInput, \PDO::PARAM_STR);
        $query->bindValue(':flat', $flatInput, \PDO::PARAM_STR);
        $query->bindValue(':text', $questionInput, \PDO::PARAM_STR);

        $database->execute($query);

        // Log successful lead save (optional)
        if ($config->debug) {
          wire('log')->message("Lead saved to database: {$mailInput}");
        }

      } catch(\Exception $e) {
        // Log database error but don't break the form submission
        if ($config->debug) {
          wire('log')->error("Failed to save lead to database: " . $e->getMessage());
        }
        // Continue with the rest of the form processing even if DB save fails
      }

      $subscriber = $promailer->subscribers->add(
        $mailInput, // email address
        1, // List ID
        true, // Confirmed
        [
          'name' => $nameInput . ' ' . $surnameInput,
          'phone' => $mobileInput,
          'lang' => $langInput,
          'flat' => $flatInput,
          'question' => $questionInput
        ]
      );
      if ($subscriber === false) {
        $error["message"] = __("Zadejte prosím platnou e-mailovou adresu");
        $error["errortype"] = "mail";
        array_push($errors, $error);
        $success = false;
      } else {
        $data["message"] = __("Vaše zpráva už letí k nám. Ozveme se vám hned, jakmile to bude možné.");

        // User message
        $from = "<EMAIL>";
        $to = $mailInput;

        $flatStr = '';
        if ($flatInput != "-") {
          if ($langInput == "cs") {
            $flatStr = ' (byt / jednotku ' . $flatInput . ')';
          }
          else {
            $flatStr = ' (apartment ' . $flatInput . ')';
          }
        }


        if ($langInput == "cs") {
          $subject = sprintf("Viladomy U Mlýna%s - děkujeme za váš zájem", $flatStr);
          $body = sprintf("Jsme rádi, že máte zájem o projekt Viladomy U Mlýna%s.", $flatStr);
          ob_start();
          include(__DIR__ . '/quote-mail-cs.php');
          $bodyHTML = ob_get_clean();
        } else {
          $subject = sprintf("Viladomy U Mlýn%s - thank you for your interest", $flatStr);
          $body = sprintf("We are glad that you are interested in project Viladomy U Mlýna%s.", $flatStr); 
          ob_start();
          include(__DIR__ . '/quote-mail-en.php');
          $bodyHTML = ob_get_clean();
        }

        wireMail()
          ->from($from, "Viladomy U Mlýna")
          ->to($to)
          ->subject($subject)
          ->body($body)
          ->bodyHTML($bodyHTML)
          ->send();

        $success = true;
      }
    }
  }
}


// If success
if ($success) {

  // Admin email
  $from = "<EMAIL>";
  //$to = ["<EMAIL>", "<EMAIL>", "<EMAIL>"];
  $to = ["<EMAIL>", "<EMAIL>"];
//  $to = ["<EMAIL>"];
  $marketingStr = $marketingInput == true ? "ANO" : "NE";

  $flatSubject = '';
  $flat = '';
  $flatHtml = '';
  if ($flatInput != "-") {
      $flatSubject = '(byt / jednotka' . $flatInput . ')';
      $flat = 'Byt / jednotka: ' . $flatInput . '';
      $flatHtml = '<b>Byt / jednotka:</b> ' . $flatInput . '<br>';
  }

  $subject = "Nový dotaz z webu Viladomy U Mlýna " . $flatSubject . " [" . strtoupper($langInput) . "]";
  $body = "
      Jméno: " . $nameInput . " \n
      Příjmení: " . $surnameInput . " \n
      E-mail: " . $mailInput . " \n
      Telefon:  " . $mobileInput . " \n
      Dotaz: " . $questionInput . " \n
      Jazyk:  " . strtoupper($langInput) . " \n\n
      " . $flat . " \n\n
      Souhlas se zpracováním svých osobních údajů: ANO \n
      Souhlas se zasíláním marketingových sdělení: " . $marketingStr . " \n\n";
  $bodyHTML = '<html><body>
      <p>
      <b>Jméno:</b> ' . $nameInput . '<br>
      <b>Příjmení:</b> ' . $surnameInput . '<br>
      <b>E-mail:</b> ' . $mailInput . '<br>
      <b>Telefon:</b> ' . $mobileInput . '<br>
      <b>Dotaz:</b> ' . $questionInput . '<br>
      <b>Jazyk:</b> ' . strtoupper($langInput) . ' <br>
      ' . $flatHtml . '
      </p>
      <p>
      <b>Souhlas se zpracováním svých osobních údajů:</b> ANO<br>
      <b>Souhlas se zasíláním marketingových sdělení:</b> ' . $marketingStr . '
      </p>
      </body></html>';

  wireMail()
      ->from($from, "Viladomy U Mlýna")
      ->to($to)
      ->subject($subject)
      ->body($body)
      ->bodyHTML($bodyHTML)
      ->send();


  $data["type"] = "success";
  http_response_code(200);
  header('Content-Type: application/json');
  echo wireEncodeJSON($data);
} else {
  $data["type"] = "error";
  $data["errors"] = $errors;
  http_response_code(200);
  header('Content-Type: application/json');
  echo wireEncodeJSON($data);
}

