<?php

namespace ProcessWire;

// Set content type to JSON
header('Content-Type: application/json');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo wireEncodeJSON([
        'success' => false,
        'error' => 'Method not allowed. Only POST requests are accepted.',
        'code' => 'METHOD_NOT_ALLOWED'
    ]);
    exit;
}

// Fixed API token for authentication
$API_TOKEN = 'viladomy_api_2024_secure_token_xyz789';

$errors = array();
$success = false;
$data = null;

// Get JSON input or form data
$input_data = null;
$content_type = $_SERVER['CONTENT_TYPE'] ?? '';

if (strpos($content_type, 'application/json') !== false) {
    $json_input = file_get_contents('php://input');
    $input_data = json_decode($json_input, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        http_response_code(400);
        echo wireEncodeJSON([
            'success' => false,
            'error' => 'Invalid JSON format',
            'code' => 'INVALID_JSON'
        ]);
        exit;
    }
} else {
    $input_data = $_POST;
}

// Validate API token
$provided_token = $input_data['api_token'] ?? $_SERVER['HTTP_X_API_TOKEN'] ?? '';

if ($provided_token !== $API_TOKEN) {
    http_response_code(401);
    echo wireEncodeJSON([
        'success' => false,
        'error' => 'Invalid or missing API token',
        'code' => 'UNAUTHORIZED'
    ]);
    exit;
}

// Sanitize and validate input data
$langInput = $sanitizer->text($input_data['lang'] ?? '');
$flatInput = $sanitizer->text($input_data['flat'] ?? '');
$mailInput = $sanitizer->email($input_data['email'] ?? '');
$nameInput = $sanitizer->text($input_data['name'] ?? '');
$surnameInput = $sanitizer->text($input_data['surname'] ?? '');
$questionInput = $sanitizer->text($input_data['question'] ?? $input_data['text'] ?? '');
$mobileInput = $sanitizer->match($input_data['phone'] ?? $input_data['mobile'] ?? '', '/^[0-9 +()*#-]+$/');

// Validation
if (empty($langInput)) {
    $error = [
        'message' => 'Language field is required',
        'field' => 'lang',
        'code' => 'REQUIRED_FIELD'
    ];
    array_push($errors, $error);
} else if ($langInput !== "en" && $langInput !== "cs") {
    $error = [
        'message' => 'Language must be "en" or "cs"',
        'field' => 'lang',
        'code' => 'INVALID_VALUE'
    ];
    array_push($errors, $error);
}

if (empty($flatInput)) {
    $error = [
        'message' => 'Flat field is required',
        'field' => 'flat',
        'code' => 'REQUIRED_FIELD'
    ];
    array_push($errors, $error);
}

if (empty($mailInput)) {
    $error = [
        'message' => 'Valid email address is required',
        'field' => 'email',
        'code' => 'REQUIRED_FIELD'
    ];
    array_push($errors, $error);
}

if (empty($nameInput)) {
    $error = [
        'message' => 'Name is required',
        'field' => 'name',
        'code' => 'REQUIRED_FIELD'
    ];
    array_push($errors, $error);
}

if (empty($surnameInput)) {
    $error = [
        'message' => 'Surname is required',
        'field' => 'surname',
        'code' => 'REQUIRED_FIELD'
    ];
    array_push($errors, $error);
}

if (empty($mobileInput)) {
    $error = [
        'message' => 'Valid phone number is required (format: +420 777 123 456)',
        'field' => 'phone',
        'code' => 'REQUIRED_FIELD'
    ];
    array_push($errors, $error);
}

// If validation errors exist, return them
if (!empty($errors)) {
    http_response_code(400);
    echo wireEncodeJSON([
        'success' => false,
        'error' => 'Validation failed',
        'code' => 'VALIDATION_ERROR',
        'errors' => $errors
    ]);
    exit;
}

// Save lead to database
try {
    $database = wire('database');
    $sql = "INSERT INTO leads (email, name, surname, phone, lang, flat, text, created)
            VALUES (:email, :name, :surname, :phone, :lang, :flat, :text, :created)";

    $query = $database->prepare($sql);
    $query->bindValue(':email', $mailInput, \PDO::PARAM_STR);
    $query->bindValue(':name', $nameInput, \PDO::PARAM_STR);
    $query->bindValue(':surname', $surnameInput, \PDO::PARAM_STR);
    $query->bindValue(':phone', $mobileInput, \PDO::PARAM_STR);
    $query->bindValue(':lang', $langInput, \PDO::PARAM_STR);
    $query->bindValue(':flat', $flatInput, \PDO::PARAM_STR);
    $query->bindValue(':text', $questionInput, \PDO::PARAM_STR);
    $query->bindValue(':created', time(), \PDO::PARAM_INT);

    $database->execute($query);
    $lead_id = $database->lastInsertId();

    // Log successful lead save
    if ($config->debug) {
        wire('log')->message("Lead saved via API: {$mailInput} (ID: {$lead_id})");
    }

    $success = true;
    $data = [
        'success' => true,
        'message' => 'Lead successfully saved',
        'lead_id' => $lead_id,
        'data' => [
            'email' => $mailInput,
            'name' => $nameInput,
            'surname' => $surnameInput,
            'phone' => $mobileInput,
            'lang' => $langInput,
            'flat' => $flatInput,
            'text' => $questionInput,
            'created' => time()
        ]
    ];

} catch(\Exception $e) {
    // Log database error
    if ($config->debug) {
        wire('log')->error("Failed to save lead via API: " . $e->getMessage());
    }
    
    http_response_code(500);
    echo wireEncodeJSON([
        'success' => false,
        'error' => 'Database error occurred',
        'code' => 'DATABASE_ERROR',
        'message' => $config->debug ? $e->getMessage() : 'Internal server error'
    ]);
    exit;
}

// Return success response
if ($success) {
    http_response_code(201);
    echo wireEncodeJSON($data);
} else {
    http_response_code(500);
    echo wireEncodeJSON([
        'success' => false,
        'error' => 'Unknown error occurred',
        'code' => 'UNKNOWN_ERROR'
    ]);
}
