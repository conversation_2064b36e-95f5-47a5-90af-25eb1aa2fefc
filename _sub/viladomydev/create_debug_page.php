<?php

/**
 * Script pro vytvoření debug API stránky
 */

// Zobraz všechny PHP chyby
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);

echo "=== Debug API Page Creator ===\n";

// Použij přímý databázový přístup
if (!file_exists('./site/config.php')) {
    echo "CHYBA: Soubor config.php nenalezen.\n";
    exit(1);
}

require_once('./site/config.php');

$dbHost = $config->dbHost ?? '127.0.0.1';
$dbName = $config->dbName ?? '';
$dbUser = $config->dbUser ?? '';
$dbPass = $config->dbPass ?? '';

try {
    $pdo = new PDO("mysql:host=$dbHost;dbname=$dbName;charset=utf8mb4", $dbUser, $dbPass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✓ Připojení k databázi úspěšné.\n";

    // Zkontroluj, jestli fieldgroup existuje
    $stmt = $pdo->prepare("SELECT id FROM fieldgroups WHERE name = 'debug-api'");
    $stmt->execute();
    $fieldgroup = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$fieldgroup) {
        echo "Vytvářím fieldgroup 'debug-api'...\n";
        $stmt = $pdo->prepare("INSERT INTO fieldgroups (name, created, modified) VALUES ('debug-api', UNIX_TIMESTAMP(), UNIX_TIMESTAMP())");
        $stmt->execute();
        $fieldgroupId = $pdo->lastInsertId();
        
        // Přidej title field
        $stmt = $pdo->prepare("INSERT INTO fieldgroups_fields (fieldgroups_id, fields_id, sort) VALUES (:fg_id, 1, 0)");
        $stmt->execute([':fg_id' => $fieldgroupId]);
        echo "✓ Fieldgroup 'debug-api' vytvořena.\n";
    } else {
        $fieldgroupId = $fieldgroup['id'];
        echo "✓ Fieldgroup 'debug-api' už existuje.\n";
    }

    // Zkontroluj template
    $stmt = $pdo->prepare("SELECT id FROM templates WHERE name = 'debug-api'");
    $stmt->execute();
    $template = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$template) {
        echo "Vytvářím template 'debug-api'...\n";
        $stmt = $pdo->prepare("INSERT INTO templates (name, filename, fieldgroups_id, created, modified) VALUES ('debug-api', 'debug-api.php', :fg_id, UNIX_TIMESTAMP(), UNIX_TIMESTAMP())");
        $stmt->execute([':fg_id' => $fieldgroupId]);
        $templateId = $pdo->lastInsertId();
        echo "✓ Template 'debug-api' vytvořen.\n";
    } else {
        $templateId = $template['id'];
        echo "✓ Template 'debug-api' už existuje.\n";
    }

    // Zkontroluj stránku
    $stmt = $pdo->prepare("SELECT id FROM pages WHERE name = 'debug-api'");
    $stmt->execute();
    $page = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$page) {
        echo "Vytvářím stránku 'debug-api'...\n";
        
        $stmt = $pdo->prepare("SELECT id FROM pages WHERE parent_id = 0 OR id = 1 LIMIT 1");
        $stmt->execute();
        $rootPage = $stmt->fetch(PDO::FETCH_ASSOC);
        $parentId = $rootPage ? $rootPage['id'] : 1;
        
        $stmt = $pdo->prepare("INSERT INTO pages (parent_id, templates_id, name, status, created, modified, sort) VALUES (:parent_id, :template_id, 'debug-api', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0)");
        $stmt->execute([':parent_id' => $parentId, ':template_id' => $templateId]);
        $pageId = $pdo->lastInsertId();
        
        echo "✓ Stránka 'debug-api' vytvořena.\n";
    } else {
        echo "✓ Stránka 'debug-api' už existuje.\n";
    }

    echo "\n=== Debug endpoint vytvořen! ===\n";
    echo "Test URL: https://dev.viladomyumlyna.cz/debug-api/\n";

} catch (Exception $e) {
    echo "CHYBA: " . $e->getMessage() . "\n";
    exit(1);
}
