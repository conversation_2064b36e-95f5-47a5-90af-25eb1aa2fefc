#!/bin/bash

echo "=== Test API Endpoint ==="

# Test 1: <PERSON><PERSON><PERSON>luj, jestli endpoint existuje (GET požadavek)
echo "1. Test existence endpointu (GET):"
curl -X GET https://dev.viladomyumlyna.cz/leads-api/ \
  -v \
  -w "\nHTTP Status: %{http_code}\n" \
  -s

echo -e "\n\n"

# Test 2: Test s verbose výstupem
echo "2. Test s verbose výstupem (POST):"
curl -X POST https://dev.viladomyumlyna.cz/leads-api/ \
  -H "Content-Type: application/json" \
  -H "X-API-Token: viladomy_api_2024_secure_token_xyz789" \
  -d '{
    "email": "<EMAIL>",
    "name": "Test",
    "surname": "User",
    "phone": "+420 123 456 789",
    "lang": "cs",
    "flat": "-"
  }' \
  -v \
  -w "\nHTTP Status: %{http_code}\nTotal Time: %{time_total}s\n"

echo -e "\n\n"

# Test 3: Test bez SSL verifikace
echo "3. Test bez SSL verifikace:"
curl -X POST https://dev.viladomyumlyna.cz/leads-api/ \
  -H "Content-Type: application/json" \
  -H "X-API-Token: viladomy_api_2024_secure_token_xyz789" \
  -d '{
    "email": "<EMAIL>",
    "name": "Test",
    "surname": "User",
    "phone": "+420 123 456 789",
    "lang": "cs",
    "flat": "-"
  }' \
  -k \
  -v \
  -w "\nHTTP Status: %{http_code}\n"

echo -e "\n=== Test dokončen ==="
